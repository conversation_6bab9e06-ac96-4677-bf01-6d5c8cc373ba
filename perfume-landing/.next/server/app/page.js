var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/app/page.js")
R.c("server/chunks/ssr/4f6d0_d0e706f4._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/Perfume_landing-page_perfume-landing_src_app_29a8f99c._.js")
R.c("server/chunks/ssr/[root-of-the-server]__f7fd4050._.js")
R.c("server/chunks/ssr/4f6d0_next_dist_client_components_44400291._.js")
R.c("server/chunks/ssr/4f6d0_next_dist_client_components_builtin_forbidden_9585c943.js")
R.c("server/chunks/ssr/4f6d0_next_dist_client_components_builtin_unauthorized_24c86cd6.js")
R.c("server/chunks/ssr/4f6d0_next_dist_client_components_builtin_global-error_dcc66705.js")
R.c("server/chunks/ssr/[root-of-the-server]__3eda48dc._.js")
R.c("server/chunks/ssr/4f6d0_6de0d627._.js")
R.m("[project]/Perfume/landing-page/perfume-landing/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Perfume/landing-page/perfume-landing/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Perfume/landing-page/perfume-landing/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Perfume/landing-page/perfume-landing/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Perfume/landing-page/perfume-landing/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Perfume/landing-page/perfume-landing/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Perfume/landing-page/perfume-landing/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Perfume/landing-page/perfume-landing/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Perfume/landing-page/perfume-landing/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Perfume/landing-page/perfume-landing/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
