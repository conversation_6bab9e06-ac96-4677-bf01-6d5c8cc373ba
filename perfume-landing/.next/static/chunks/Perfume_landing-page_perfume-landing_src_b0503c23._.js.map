{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Perfume/landing-page/perfume-landing/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,6NAAO,EAAC,IAAA,oMAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Perfume/landing-page/perfume-landing/src/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\nimport { cn } from \"@/lib/utils\";\nimport React, { ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <main>\n      <div\n        className={cn(\n          \"relative flex flex-col  h-[100vh] items-center justify-center bg-zinc-50 dark:bg-zinc-900  text-slate-950 transition-bg\",\n          className\n        )}\n        {...props}\n      >\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div\n            //   I'm sorry but this is what peak developer performance looks like // trigger warning\n            className={cn(\n              `\n            [--white-gradient:repeating-linear-gradient(100deg,var(--white)_0%,var(--white)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--white)_16%)]\n            [--dark-gradient:repeating-linear-gradient(100deg,var(--black)_0%,var(--black)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--black)_16%)]\n            [--aurora:repeating-linear-gradient(100deg,var(--blue-500)_10%,var(--indigo-300)_15%,var(--blue-300)_20%,var(--violet-200)_25%,var(--blue-400)_30%)]\n            [background-image:var(--white-gradient),var(--aurora)]\n            dark:[background-image:var(--dark-gradient),var(--aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px] invert dark:invert-0\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--white-gradient),var(--aurora)] \n            after:dark:[background-image:var(--dark-gradient),var(--aurora)]\n            after:[background-size:200%,_100%] \n            after:[animation:aurora_60s_linear_infinite] after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n\n              showRadialGradient &&\n                `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,var(--transparent)_70%)]`\n            )}\n          ></div>\n        </div>\n        {children}\n      </div>\n    </main>\n  );\n};\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AASO,MAAM,mBAAmB;QAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,iPAAC;kBACC,cAAA,iPAAC;YACC,WAAW,IAAA,gLAAE,EACX,2HACA;YAED,GAAG,KAAK;;8BAET,iPAAC;oBAAI,WAAU;8BACb,cAAA,iPAAC;wBACC,wFAAwF;wBACxF,WAAW,IAAA,gLAAE,EACV,8tCAgBD,sBACG;;;;;;;;;;;gBAIR;;;;;;;;;;;;AAIT;KA5Ca", "debugId": null}}]}