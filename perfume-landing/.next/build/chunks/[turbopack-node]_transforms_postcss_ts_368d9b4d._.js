module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Perfume/landing-page/perfume-landing/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/4f6d0_99df9948._.js",
  "build/chunks/[root-of-the-server]__911c2cda._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Perfume/landing-page/perfume-landing/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];