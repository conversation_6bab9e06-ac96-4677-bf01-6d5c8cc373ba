!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach(function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}}),e.default=t,Object.freeze(e)}var i=n(e),s=React,o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,n){var i,s={},r=null,c=null;for(i in void 0!==n&&(r=""+n),void 0!==e.key&&(r=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,i)&&!u.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===s[i]&&(s[i]=e[i]);return{$$typeof:o,type:t,key:r,ref:c,props:s,_owner:l.current}}const h=r,d=c,p=c,m=e.createContext({});function f(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const g="undefined"!=typeof window,y=g?e.useLayoutEffect:e.useEffect,v=e.createContext(null);function x(t,e){-1===t.indexOf(e)&&t.push(e)}function w(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}function T([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}const P=(t,e,n)=>n>e?e:n<t?t:n;let b=()=>{};const S={},E=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function A(t){return"object"==typeof t&&null!==t}const M=t=>/^0[^.\s]+$/u.test(t);function C(t){let e;return()=>(void 0===e&&(e=t()),e)}const V=t=>t,R=(t,e)=>n=>e(t(n)),D=(...t)=>t.reduce(R),k=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class L{constructor(){this.subscriptions=[]}add(t){return x(this.subscriptions,t),()=>w(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const O=t=>1e3*t,j=t=>t/1e3;function B(t,e){return e?t*(1e3/e):0}const I=new Set;const F=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t},W=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function U(t,e,n,i){if(t===e&&n===i)return V;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=W(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:W(s(t),e,i)}const N=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,$=t=>e=>1-t(1-e),z=U(.33,1.53,.69,.99),X=$(z),H=N(X),Y=t=>(t*=2)<1?.5*X(t):.5*(2-Math.pow(2,-10*(t-1))),K=t=>1-Math.sin(Math.acos(t)),G=$(K),_=N(K),Z=U(.42,0,1,1),q=U(0,0,.58,1),J=U(.42,0,.58,1);const Q=t=>Array.isArray(t)&&"number"!=typeof t[0];function tt(t,e){return Q(t)?t[F(0,t.length,e)]:t}const et=t=>Array.isArray(t)&&"number"==typeof t[0],nt={linear:V,easeIn:Z,easeInOut:J,easeOut:q,circIn:K,circInOut:_,circOut:G,backIn:X,backInOut:H,backOut:z,anticipate:Y},it=t=>{if(et(t)){t.length;const[e,n,i,s]=t;return U(e,n,i,s)}return"string"==typeof t?nt[t]:t},st=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ot={value:null,addProjectionMetrics:null};function rt(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=st.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&ot.value&&ot.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,c.process(t)))}};return c}(o,e?n:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:d,render:p,postRender:m}=r,f=()=>{const o=S.useManualTiming?s.timestamp:performance.now();n=!1,S.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(f))};return{schedule:st.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<st.length;e++)r[st[e]].cancel(t)},state:s,steps:r}}const{schedule:at,cancel:lt,state:ut,steps:ct}=rt("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:V,!0);let ht;function dt(){ht=void 0}const pt={now:()=>(void 0===ht&&pt.set(ut.isProcessing||S.useManualTiming?ut.timestamp:performance.now()),ht),set:t=>{ht=t,queueMicrotask(dt)}},mt={layout:0,mainThread:0,waapi:0},ft=t=>e=>"string"==typeof e&&e.startsWith(t),gt=ft("--"),yt=ft("var(--"),vt=t=>!!yt(t)&&xt.test(t.split("/*")[0].trim()),xt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,wt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Tt={...wt,transform:t=>P(0,1,t)},Pt={...wt,default:1},bt=t=>Math.round(1e5*t)/1e5,St=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Et=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,At=(t,e)=>n=>Boolean("string"==typeof n&&Et.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Mt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(St);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ct={...wt,transform:t=>Math.round((t=>P(0,255,t))(t))},Vt={test:At("rgb","red"),parse:Mt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Ct.transform(t)+", "+Ct.transform(e)+", "+Ct.transform(n)+", "+bt(Tt.transform(i))+")"};const Rt={test:At("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Vt.transform},Dt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),kt=Dt("deg"),Lt=Dt("%"),Ot=Dt("px"),jt=Dt("vh"),Bt=Dt("vw"),It=(()=>({...Lt,parse:t=>Lt.parse(t)/100,transform:t=>Lt.transform(100*t)}))(),Ft={test:At("hsl","hue"),parse:Mt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+Lt.transform(bt(e))+", "+Lt.transform(bt(n))+", "+bt(Tt.transform(i))+")"},Wt={test:t=>Vt.test(t)||Rt.test(t)||Ft.test(t),parse:t=>Vt.test(t)?Vt.parse(t):Ft.test(t)?Ft.parse(t):Rt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Vt.transform(t):Ft.transform(t),getAnimatableNone:t=>{const e=Wt.parse(t);return e.alpha=0,Wt.transform(e)}},Ut=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Nt="number",$t="color",zt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Xt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(zt,t=>(Wt.test(t)?(i.color.push(o),s.push($t),n.push(Wt.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(Nt),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function Ht(t){return Xt(t).values}function Yt(t){const{split:e,types:n}=Xt(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===Nt?bt(t[o]):e===$t?Wt.transform(t[o]):t[o]}return s}}const Kt=t=>"number"==typeof t?0:Wt.test(t)?Wt.getAnimatableNone(t):t;const Gt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(St)?.length||0)+(t.match(Ut)?.length||0)>0},parse:Ht,createTransformer:Yt,getAnimatableNone:function(t){const e=Ht(t);return Yt(t)(e.map(Kt))}};function _t(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Zt({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=_t(a,i,t+1/3),o=_t(a,i,t),r=_t(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}function qt(t,e){return n=>n>0?e:t}const Jt=(t,e,n)=>t+(e-t)*n,Qt=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},te=[Rt,Vt,Ft];function ee(t){const e=(n=t,te.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Ft&&(i=Zt(i)),i}const ne=(t,e)=>{const n=ee(t),i=ee(e);if(!n||!i)return qt(t,e);const s={...n};return t=>(s.red=Qt(n.red,i.red,t),s.green=Qt(n.green,i.green,t),s.blue=Qt(n.blue,i.blue,t),s.alpha=Jt(n.alpha,i.alpha,t),Vt.transform(s))},ie=new Set(["none","hidden"]);function se(t,e){return ie.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function oe(t,e){return n=>Jt(t,e,n)}function re(t){return"number"==typeof t?oe:"string"==typeof t?vt(t)?qt:Wt.test(t)?ne:ue:Array.isArray(t)?ae:"object"==typeof t?Wt.test(t)?ne:le:qt}function ae(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>re(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function le(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=re(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const ue=(t,e)=>{const n=Gt.createTransformer(e),i=Xt(t),s=Xt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?ie.has(t)&&!s.values.length||ie.has(e)&&!i.values.length?se(t,e):D(ae(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):qt(t,e)};function ce(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Jt(t,e,n);return re(t)(t,e)}const he=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>at.update(e,t),stop:()=>lt(e),now:()=>ut.isProcessing?ut.timestamp:pt.now()}},de=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},pe=2e4;function me(t){let e=0;let n=t.next(e);for(;!n.done&&e<pe;)e+=50,n=t.next(e);return e>=pe?1/0:e}function fe(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(me(i),pe);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:j(s)}}function ge(t,e,n){const i=Math.max(e-5,0);return B(n-t(i),e-i)}const ye=100,ve=10,xe=1,we=0,Te=800,Pe=.3,be=.3,Se={granular:.01,default:2},Ee={granular:.005,default:.5},Ae=.01,Me=10,Ce=.05,Ve=1,Re=.001;function De({duration:t=Te,bounce:e=Pe,velocity:n=we,mass:i=xe}){let s,o,r=1-e;r=P(Ce,Ve,r),t=P(Ae,Me,j(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=Le(e,r),l=Math.exp(-s);return Re-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=Le(Math.pow(e,2),r);return(-s(e)+Re>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<ke;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=O(t),isNaN(a))return{stiffness:ye,damping:ve,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const ke=12;function Le(t,e){return t*Math.sqrt(1-e*e)}const Oe=["duration","bounce"],je=["stiffness","damping","mass"];function Be(t,e){return e.some(e=>void 0!==t[e])}function Ie(t=be,e=Pe){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:we,stiffness:ye,damping:ve,mass:xe,isResolvedFromDuration:!1,...t};if(!Be(t,je)&&Be(t,Oe))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*P(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:xe,stiffness:s,damping:o}}else{const n=De(t);e={...e,...n,mass:xe},e.isResolvedFromDuration=!0}return e}({...n,velocity:-j(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),g=r-o,y=j(Math.sqrt(l/c)),v=Math.abs(g)<5;let x;if(i||(i=v?Se.granular:Se.default),s||(s=v?Ee.granular:Ee.default),f<1){const t=Le(y,f);x=e=>{const n=Math.exp(-f*y*e);return r-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),i=Math.min(t*e,300);return r-n*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0===t?m:0;f<1&&(n=0===t?O(m):ge(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(me(w),pe),e=de(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Fe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let w,T;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,T=Ie({keyframes:[d.value,p(d.value)],velocity:ge(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,x(t),P(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&x(t),d)}}}function We(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||S.mix||ce,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||V:e;o=D(t,o)}i.push(o)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=k(t[i],t[i+1],n);return a[i](s)};return n?e=>u(P(t[0],t[o-1],e)):u}function Ue(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=k(0,e,i);t.push(Jt(n,1,s))}}function Ne(t){const e=[0];return Ue(e,t.length-1),e}function $e(t,e){return t.map(t=>t*e)}function ze(t,e){return t.map(()=>e||J).splice(0,t.length-1)}function Xe({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=Q(i)?i.map(it):it(i),o={done:!1,value:e[0]},r=We($e(n&&n.length===e.length?n:Ne(e),t),e,{ease:Array.isArray(s)?s:ze(e,s)});return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}Ie.applyToOptions=t=>{const e=fe(t,100,Ie);return t.ease=e.ease,t.duration=O(e.duration),t.type="keyframes",t};const He=t=>null!==t;function Ye(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(He),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const Ke={decay:Fe,inertia:Fe,tween:Xe,keyframes:Xe,spring:Ie};function Ge(t){"string"==typeof t.type&&(t.type=Ke[t.type])}class _e{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ze=t=>t/100;class qe extends _e{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==pt.now()&&this.tick(pt.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},mt.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ge(t);const{type:e=Xe,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Xe;a!==Xe&&"number"!=typeof r[0]&&(this.mixKeyframes=D(Ze,ce(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=me(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,x=n;if(c){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===h?(n=1-n,d&&(n-=d/r)):"mirror"===h&&(x=o)),v=P(0,1,n)*r}const w=y?{done:!1,value:u[0]}:x.next(v);s&&(w.value=s(w.value));let{done:T}=w;y||null===a||(T=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return b&&p!==Fe&&(w.value=Ye(u,this.options,f,this.speed)),m&&m(w.value),b&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return j(this.calculatedDuration)}get time(){return j(this.currentTime)}set time(t){t=O(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(pt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=j(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=he,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(pt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,mt.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Je(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Qe=t=>180*t/Math.PI,tn=t=>{const e=Qe(Math.atan2(t[1],t[0]));return nn(e)},en={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:tn,rotateZ:tn,skewX:t=>Qe(Math.atan(t[1])),skewY:t=>Qe(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},nn=t=>((t%=360)<0&&(t+=360),t),sn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),on=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),rn={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:sn,scaleY:on,scale:t=>(sn(t)+on(t))/2,rotateX:t=>nn(Qe(Math.atan2(t[6],t[5]))),rotateY:t=>nn(Qe(Math.atan2(-t[2],t[0]))),rotateZ:tn,rotate:tn,skewX:t=>Qe(Math.atan(t[4])),skewY:t=>Qe(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function an(t){return t.includes("scale")?1:0}function ln(t,e){if(!t||"none"===t)return an(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=rn,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=en,s=e}if(!s)return an(e);const o=i[e],r=s[1].split(",").map(cn);return"function"==typeof o?o(r):r[o]}const un=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return ln(n,e)};function cn(t){return parseFloat(t.trim())}const hn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],dn=(()=>new Set(hn))(),pn=t=>t===wt||t===Ot,mn=new Set(["x","y","z"]),fn=hn.filter(t=>!mn.has(t));const gn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ln(e,"x"),y:(t,{transform:e})=>ln(e,"y")};gn.translateX=gn.x,gn.translateY=gn.y;const yn=new Set;let vn=!1,xn=!1,wn=!1;function Tn(){if(xn){const t=Array.from(yn).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return fn.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}xn=!1,vn=!1,yn.forEach(t=>t.complete(wn)),yn.clear()}function Pn(){yn.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(xn=!0)})}function bn(){wn=!0,Pn(),Tn(),wn=!1}class Sn{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(yn.add(this),vn||(vn=!0,at.read(Pn),at.resolveKeyframes(Tn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}Je(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),yn.delete(this)}cancel(){"scheduled"===this.state&&(yn.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const En=t=>t.startsWith("--");function An(t,e,n){En(e)?t.style.setProperty(e,n):t.style[e]=n}const Mn=C(()=>void 0!==window.ScrollTimeline),Cn={};function Vn(t,e){const n=C(t);return()=>Cn[e]??n()}const Rn=Vn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),Dn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,kn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Dn([0,.65,.55,1]),circOut:Dn([.55,0,1,.45]),backIn:Dn([.31,.01,.66,-.59]),backOut:Dn([.33,1.53,.69,.99])};function Ln(t,e){return t?"function"==typeof t?Rn()?de(t,e):"ease-out":et(t)?Dn(t):Array.isArray(t)?t.map(t=>Ln(t,e)||kn.easeOut):kn[t]:void 0}function On(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=Ln(a,s);Array.isArray(h)&&(c.easing=h),ot.value&&mt.waapi++;const d={delay:i,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);const p=t.animate(c,d);return ot.value&&p.finished.finally(()=>{mt.waapi--}),p}function jn(t){return"function"==typeof t&&"applyToOptions"in t}function Bn({type:t,...e}){return jn(t)&&Rn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class In extends _e{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=Bn(t);this.animation=On(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Ye(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):An(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return j(Number(t))}get time(){return j(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=O(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Mn()?(this.animation.timeline=t,V):e(this)}}const Fn={anticipate:Y,backInOut:H,circInOut:_};function Wn(t){"string"==typeof t.ease&&t.ease in Fn&&(t.ease=Fn[t.ease])}class Un extends In{constructor(t){Wn(t),Ge(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new qe({...o,autoplay:!1}),a=O(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const Nn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Gt.test(t)&&"0"!==t||t.startsWith("url(")));function $n(t){t.duration=0,t.type}const zn=new Set(["opacity","clipPath","filter","transform"]),Xn=C(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Hn(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t,a=e?.owner?.current;if(!(a instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=e.owner.getProps();return Xn()&&n&&zn.has(n)&&("transform"!==n||!u)&&!l&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}class Yn extends _e{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=pt.now();const h={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||Sn;this.keyframeResolver=new d(r,(t,e,n)=>this.onKeyframesResolved(t,e,h,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=pt.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=Nn(s,e),a=Nn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||jn(n))&&i)}(t,s,o,r)||(!S.instantAnimations&&a||u?.(Ye(t,n,e)),t[0]=t[t.length-1],$n(n),n.repeat=0);const c={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},h=!l&&Hn(c)?new Un({...c,element:c.motionValue.owner.current}):new qe(c);h.finished.then(()=>this.notifyFinished()).catch(V),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),bn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class Kn{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Gn extends Kn{then(t,e){return this.finished.finally(t).then(()=>{})}}class _n extends In{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const Zn=new WeakMap,qn=(t,e="")=>`${t}:${e}`;function Jn(t){const e=Zn.get(t)||new Map;return Zn.set(t,e),e}const Qn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ti(t){const e=Qn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function ei(t,e,n=1){const[i,s]=ti(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return E(t)?parseFloat(t):t}return vt(s)?ei(s,e,n+1):s}function ni(t,e){return t?.[e]??t?.default??t}const ii=new Set(["width","height","top","left","right","bottom",...hn]),si=t=>e=>e.test(t),oi=[wt,Ot,Lt,kt,Bt,jt,{test:t=>"auto"===t,parse:t=>t}],ri=t=>oi.find(si(t));function ai(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||M(t))}const li=new Set(["brightness","contrast","saturate","opacity"]);function ui(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(St)||[];if(!i)return t;const s=n.replace(i,"");let o=li.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const ci=/\b([a-z-]*)\(.*?\)/gu,hi={...Gt,getAnimatableNone:t=>{const e=t.match(ci);return e?e.map(ui).join(" "):t}},di={...wt,transform:Math.round},pi={rotate:kt,rotateX:kt,rotateY:kt,rotateZ:kt,scale:Pt,scaleX:Pt,scaleY:Pt,scaleZ:Pt,skew:kt,skewX:kt,skewY:kt,distance:Ot,translateX:Ot,translateY:Ot,translateZ:Ot,x:Ot,y:Ot,z:Ot,perspective:Ot,transformPerspective:Ot,opacity:Tt,originX:It,originY:It,originZ:Ot},mi={borderWidth:Ot,borderTopWidth:Ot,borderRightWidth:Ot,borderBottomWidth:Ot,borderLeftWidth:Ot,borderRadius:Ot,radius:Ot,borderTopLeftRadius:Ot,borderTopRightRadius:Ot,borderBottomRightRadius:Ot,borderBottomLeftRadius:Ot,width:Ot,maxWidth:Ot,height:Ot,maxHeight:Ot,top:Ot,right:Ot,bottom:Ot,left:Ot,padding:Ot,paddingTop:Ot,paddingRight:Ot,paddingBottom:Ot,paddingLeft:Ot,margin:Ot,marginTop:Ot,marginRight:Ot,marginBottom:Ot,marginLeft:Ot,backgroundPositionX:Ot,backgroundPositionY:Ot,...pi,zIndex:di,fillOpacity:Tt,strokeOpacity:Tt,numOctaves:di},fi={...mi,color:Wt,backgroundColor:Wt,outlineColor:Wt,fill:Wt,stroke:Wt,borderColor:Wt,borderTopColor:Wt,borderRightColor:Wt,borderBottomColor:Wt,borderLeftColor:Wt,filter:hi,WebkitFilter:hi},gi=t=>fi[t];function yi(t,e){let n=gi(t);return n!==hi&&(n=Gt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const vi=new Set(["auto","none","0"]);class xi extends Sn{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),vt(i))){const s=ei(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!ii.has(n)||2!==t.length)return;const[i,s]=t,o=ri(i),r=ri(s);if(o!==r)if(pn(o)&&pn(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else gn[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||ai(t[e]))&&n.push(e);n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!vi.has(e)&&Xt(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=yi(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=gn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=gn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}const wi=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function Ti(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&wi.has(e)&&(t[n]=t[n]+"px")}const Pi=C(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),bi=new Set(["opacity","clipPath","filter","transform"]);function Si(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}function Ei(t){return(e,n)=>{const i=Si(e),s=[];for(const e of i){const i=t(e,n);s.push(i)}return()=>{for(const t of s)t()}}}const Ai=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Mi{constructor(){this.latest={},this.values=new Map}set(t,e,n,i,s=!0){const o=this.values.get(t);o&&o.onRemove();const r=()=>{const i=e.get();this.latest[t]=s?Ai(i,mi[t]):i,n&&at.render(n)};r();const a=e.on("change",r);i&&e.addDependent(i);const l=()=>{a(),n&&lt(n),this.values.delete(t),i&&e.removeDependent(i)};return this.values.set(t,{value:e,onRemove:l}),l}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}function Ci(t){const e=new WeakMap,n=[];return(i,s)=>{const o=e.get(i)??new Mi;e.set(i,o);for(const e in s){const r=s[e],a=t(i,o,e,r);n.push(a)}return()=>{for(const t of n)t()}}}const Vi=(t,e,n,i)=>{const s=function(t,e){if(!(e in t))return!1;const n=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),e)||Object.getOwnPropertyDescriptor(t,e);return n&&"function"==typeof n.set}(t,n),o=s?n:n.startsWith("data")||n.startsWith("aria")?n.replace(/([A-Z])/g,t=>`-${t.toLowerCase()}`):n;const r=s?()=>{t[o]=e.latest[n]}:()=>{const i=e.latest[n];null==i?t.removeAttribute(o):t.setAttribute(o,String(i))};return e.set(n,i,r)},Ri=Ei(Ci(Vi)),Di=Ci((t,e,n,i)=>e.set(n,i,()=>{t[n]=e.latest[n]},void 0,!1));function ki(t){return A(t)&&"offsetHeight"in t}const Li={current:void 0};class Oi{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{const e=pt.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=pt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new L);const n=this.events[t].add(e);return"change"===t?()=>{n(),at.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return Li.current&&Li.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=pt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return B(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ji(t,e){return new Oi(t,e)}const Bi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const Ii=new Set(["originX","originY","originZ"]),Fi=(t,e,n,i)=>{let s,o;return dn.has(n)?(e.get("transform")||(ki(t)||e.get("transformBox")||Fi(t,e,"transformBox",new Oi("fill-box")),e.set("transform",new Oi("none"),()=>{t.style.transform=function(t){let e="",n=!0;for(let i=0;i<hn.length;i++){const s=hn[i],o=t.latest[s];if(void 0===o)continue;let r=!0;r="number"==typeof o?o===(s.startsWith("scale")?1:0):0===parseFloat(o),r||(n=!1,e+=`${Bi[s]||s}(${t.latest[s]}) `)}return n?"none":e.trim()}(e)})),o=e.get("transform")):Ii.has(n)?(e.get("transformOrigin")||e.set("transformOrigin",new Oi(""),()=>{const n=e.latest.originX??"50%",i=e.latest.originY??"50%",s=e.latest.originZ??0;t.style.transformOrigin=`${n} ${i} ${s}`}),o=e.get("transformOrigin")):s=En(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,i,s,o)},Wi=Ei(Ci(Fi)),Ui=Ot.transform;const Ni=Ei(Ci((t,e,n,i)=>{if(n.startsWith("path"))return function(t,e,n,i){return at.render(()=>t.setAttribute("pathLength","1")),"pathOffset"===n?e.set(n,i,()=>t.setAttribute("stroke-dashoffset",Ui(-e.latest[n]))):(e.get("stroke-dasharray")||e.set("stroke-dasharray",new Oi("1 1"),()=>{const{pathLength:n=1,pathSpacing:i}=e.latest;t.setAttribute("stroke-dasharray",`${Ui(n)} ${Ui(i??1-Number(n))}`)}),e.set(n,i,void 0,e.get("stroke-dasharray")))}(t,e,n,i);if(n.startsWith("attr"))return Vi(t,e,function(t){return t.replace(/^attr([A-Z])/,(t,e)=>e.toLowerCase())}(n),i);return(n in t.style?Fi:Vi)(t,e,n,i)}));const{schedule:$i,cancel:zi}=rt(queueMicrotask,!1),Xi={x:!1,y:!1};function Hi(){return Xi.x||Xi.y}function Yi(t){return"x"===t||"y"===t?Xi[t]?null:(Xi[t]=!0,()=>{Xi[t]=!1}):Xi.x||Xi.y?null:(Xi.x=Xi.y=!0,()=>{Xi.x=Xi.y=!1})}function Ki(t,e){const n=Si(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Gi(t){return!("touch"===t.pointerType||Hi())}function _i(t,e,n={}){const[i,s,o]=Ki(t,n),r=t=>{if(!Gi(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Gi(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}const Zi=(t,e)=>!!e&&(t===e||Zi(t,e.parentElement)),qi=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Ji=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Qi=new WeakSet;function ts(t){return e=>{"Enter"===e.key&&t(e)}}function es(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ns(t){return qi(t)&&!Hi()}function is(t,e,n={}){const[i,s,o]=Ki(t,n),r=t=>{const i=t.currentTarget;if(!ns(t))return;Qi.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Qi.has(i)&&Qi.delete(i),ns(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||Zi(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),ki(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=ts(()=>{if(Qi.has(n))return;es(n,"down");const t=ts(()=>{es(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>es(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s)),e=t,Ji.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}function ss(t,e){const n=window.getComputedStyle(t);return En(e)?n.getPropertyValue(e):n[e]}function os(t){return A(t)&&"ownerSVGElement"in t}const rs=new WeakMap;let as;const ls=(t,e,n)=>(i,s)=>s&&s[0]?s[0][t+"Size"]:os(i)&&"getBBox"in i?i.getBBox()[e]:i[n],us=ls("inline","width","offsetWidth"),cs=ls("block","height","offsetHeight");function hs({target:t,borderBoxSize:e}){rs.get(t)?.forEach(n=>{n(t,{get width(){return us(t,e)},get height(){return cs(t,e)}})})}function ds(t){t.forEach(hs)}function ps(t,e){as||"undefined"!=typeof ResizeObserver&&(as=new ResizeObserver(ds));const n=Si(t);return n.forEach(t=>{let n=rs.get(t);n||(n=new Set,rs.set(t,n)),n.add(e),as?.observe(t)}),()=>{n.forEach(t=>{const n=rs.get(t);n?.delete(e),n?.size||as?.unobserve(t)})}}const ms=new Set;let fs;function gs(t){return ms.add(t),fs||(fs=()=>{const t={get width(){return window.innerWidth},get height(){return window.innerHeight}};ms.forEach(e=>e(t))},window.addEventListener("resize",fs)),()=>{ms.delete(t),ms.size||"function"!=typeof fs||(window.removeEventListener("resize",fs),fs=void 0)}}function ys(t,e){return"function"==typeof t?gs(t):ps(t,e)}function vs(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return at.preUpdate(i,!0),()=>lt(i)}function xs(){const{value:t}=ot;null!==t?(t.frameloop.rate.push(ut.delta),t.animations.mainThread.push(mt.mainThread),t.animations.waapi.push(mt.waapi),t.animations.layout.push(mt.layout)):lt(xs)}function ws(t){return t.reduce((t,e)=>t+e,0)/t.length}function Ts(t,e=ws){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const Ps=t=>Math.round(1e3/t);function bs(){ot.value=null,ot.addProjectionMetrics=null}function Ss(){const{value:t}=ot;if(!t)throw new Error("Stats are not being measured");bs(),lt(xs);const e={frameloop:{setup:Ts(t.frameloop.setup),rate:Ts(t.frameloop.rate),read:Ts(t.frameloop.read),resolveKeyframes:Ts(t.frameloop.resolveKeyframes),preUpdate:Ts(t.frameloop.preUpdate),update:Ts(t.frameloop.update),preRender:Ts(t.frameloop.preRender),render:Ts(t.frameloop.render),postRender:Ts(t.frameloop.postRender)},animations:{mainThread:Ts(t.animations.mainThread),waapi:Ts(t.animations.waapi),layout:Ts(t.animations.layout)},layoutProjection:{nodes:Ts(t.layoutProjection.nodes),calculatedTargetDeltas:Ts(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:Ts(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=Ps(n.min),n.max=Ps(n.max),n.avg=Ps(n.avg),[n.min,n.max]=[n.max,n.min],e}function Es(t){return os(t)&&"svg"===t.tagName}function As(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}function Ms(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=We(t[1+n],t[2+n],t[3+n]);return e?s(i):s}function Cs(t){const e=[];Li.current=e;const n=t();Li.current=void 0;const i=ji(n);return function(t,e,n){const i=()=>e.set(n()),s=()=>at.preRender(i,!1,!0),o=t.map(t=>t.on("change",s));e.on("destroy",()=>{o.forEach(t=>t()),lt(i)})}(e,i,t),i}const Vs=t=>Boolean(t&&t.getVelocity);function Rs(t,e,n){const i=t.get();let s,o=null,r=i;const a="string"==typeof i?i.replace(/[\d.-]/g,""):void 0,l=()=>{o&&(o.stop(),o=null)},u=()=>{l(),o=new qe({keyframes:[ks(t.get()),ks(r)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:s})};if(t.attach((e,n)=>(r=e,s=t=>n(Ds(t,a)),at.postRender(u),t.get()),l),Vs(e)){const n=e.on("change",e=>t.set(Ds(e,a))),i=t.on("destroy",n);return()=>{n(),i()}}return l}function Ds(t,e){return e?t+e:t}function ks(t){return"number"==typeof t?t:parseFloat(t)}const Ls=[...oi,Wt,Gt],Os=t=>Ls.find(si(t));function js(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let Bs={},Is=null;const Fs=(t,e)=>{Bs[t]=e},Ws=()=>{Is||(Is=document.createElement("style"),Is.id="motion-view");let t="";for(const e in Bs){const n=Bs[e];t+=`${e} {\n`;for(const[e,i]of Object.entries(n))t+=`  ${e}: ${i};\n`;t+="}\n"}Is.textContent=t,document.head.appendChild(Is),Bs={}},Us=()=>{Is&&Is.parentElement&&Is.parentElement.removeChild(Is)};function Ns(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function $s(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}function zs(){return document.getAnimations().filter($s)}const Xs=["layout","enter","exit","new","old"];function Hs(t){const{update:e,targets:n,options:i}=t;if(!document.startViewTransition)return new Promise(async t=>{await e(),t(new Kn([]))});(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",n)||Fs(":root",{"view-transition-name":"none"}),Fs("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),Ws();const s=document.startViewTransition(async()=>{await e()});return s.finished.finally(()=>{Us()}),new Promise(t=>{s.ready.then(()=>{const e=zs(),s=[];n.forEach((t,e)=>{for(const n of Xs){if(!t[n])continue;const{keyframes:o,options:r}=t[n];for(let[t,a]of Object.entries(o)){if(!a)continue;const o={...ni(i,t),...ni(r,t)},l=js(n);if("opacity"===t&&!Array.isArray(a)){a=["new"===l?0:1,a]}"function"==typeof o.delay&&(o.delay=o.delay(0,1)),o.duration&&(o.duration=O(o.duration)),o.delay&&(o.delay=O(o.delay));const u=new In({...o,element:document.documentElement,name:t,pseudoElement:`::view-transition-${l}(${e})`,keyframes:a});s.push(u)}}});for(const t of e){if("finished"===t.playState)continue;const{effect:e}=t;if(!(e&&e instanceof KeyframeEffect))continue;const{pseudoElement:o}=e;if(!o)continue;const r=Ns(o);if(!r)continue;const a=n.get(r.layer);if(a)Ys(a,"enter")&&Ys(a,"exit")&&e.getKeyframes().some(t=>t.mixBlendMode)?s.push(new _n(t)):t.cancel();else{const n="group"===r.type?"layout":"";let o={...ni(i,n)};o.duration&&(o.duration=O(o.duration)),o=Bn(o);const a=Ln(o.ease,o.duration);e.updateTiming({delay:O(o.delay??0),duration:o.duration,easing:a}),s.push(new _n(t))}}t(new Kn(s))})})}function Ys(t,e){return t?.[e]?.keyframes.opacity}let Ks=[],Gs=null;function _s(){Gs=null;const[t]=Ks;var e;t&&(w(Ks,e=t),Gs=e,Hs(e).then(t=>{e.notifyReady(t),t.finished.finally(_s)}))}function Zs(){for(let t=Ks.length-1;t>=0;t--){const e=Ks[t],{interrupt:n}=e.options;if("immediate"===n){const n=Ks.slice(0,t+1).map(t=>t.update),i=Ks.slice(t+1);e.update=()=>{n.forEach(t=>t())},Ks=[e,...i];break}}Gs&&"immediate"!==Ks[0]?.options.interrupt||_s()}class qs{constructor(t,e={}){var n;this.currentSubject="root",this.targets=new Map,this.notifyReady=V,this.readyPromise=new Promise(t=>{this.notifyReady=t}),this.update=t,this.options={interrupt:"wait",...e},n=this,Ks.push(n),$i.render(Zs)}get(t){return this.currentSubject=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentSubject:i,targets:s}=this;s.has(i)||s.set(i,{});s.get(i)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const Js=at,Qs=st.reduce((t,e)=>(t[e]=t=>lt(t),t),{}),to=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class eo extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=ki(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function no({children:t,isPresent:n,anchorX:s,root:o}){const r=e.useId(),a=e.useRef(null),l=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=e.useContext(to);return e.useInsertionEffect(()=>{const{width:t,height:e,top:i,left:c,right:h}=l.current;if(n||!a.current||!t||!e)return;const d="left"===s?`left: ${c}`:`right: ${h}`;a.current.dataset.motionPopId=r;const p=document.createElement("style");u&&(p.nonce=u);const m=o??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`\n          [data-motion-pop-id="${r}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${d}px !important;\n            top: ${i}px !important;\n          }\n        `),()=>{m.contains(p)&&m.removeChild(p)}},[n]),d(eo,{isPresent:n,childRef:a,sizeRef:l,children:i.cloneElement(t,{ref:a})})}const io=({children:t,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l,anchorX:u,root:c})=>{const h=f(so),p=e.useId();let m=!0,g=e.useMemo(()=>(m=!1,{id:p,initial:n,isPresent:s,custom:r,onExitComplete:t=>{h.set(t,!0);for(const t of h.values())if(!t)return;o&&o()},register:t=>(h.set(t,!1),()=>h.delete(t))}),[s,h,o]);return a&&m&&(g={...g}),e.useMemo(()=>{h.forEach((t,e)=>h.set(e,!1))},[s]),i.useEffect(()=>{!s&&!h.size&&o&&o()},[s]),"popLayout"===l&&(t=d(no,{isPresent:s,anchorX:u,root:c,children:t})),d(v.Provider,{value:g,children:t})};function so(){return new Map}function oo(t=!0){const n=e.useContext(v);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=e.useId();e.useEffect(()=>{if(t)return o(r)},[t]);const a=e.useCallback(()=>t&&s&&s(r),[r,s,t]);return!i&&s?[!1,a]:[!0]}const ro=t=>t.key||"";function ao(t){const n=[];return e.Children.forEach(t,t=>{e.isValidElement(t)&&n.push(t)}),n}const lo=e.createContext(null);function uo(t){return t.max-t.min}function co(t,e,n,i=.5){t.origin=i,t.originPoint=Jt(e.min,e.max,t.origin),t.scale=uo(n)/uo(e),t.translate=Jt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function ho(t,e,n,i){co(t.x,e.x,n.x,i?i.originX:void 0),co(t.y,e.y,n.y,i?i.originY:void 0)}function po(t,e,n){t.min=n.min+e.min,t.max=t.min+uo(e)}function mo(t,e,n){t.min=e.min-n.min,t.max=t.min+uo(e)}function fo(t,e,n){mo(t.x,e.x,n.x),mo(t.y,e.y,n.y)}const go=t=>!t.isLayoutDirty&&t.willUpdate(!1);function yo(){const t=new Set,e=new WeakMap,n=()=>t.forEach(go);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}const vo=t=>null!==t;const xo={type:"spring",stiffness:500,damping:25,restSpeed:10},wo={type:"keyframes",duration:.8},To={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Po=(t,{keyframes:e})=>e.length>2?wo:dn.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:xo:To;const bo=(t,e,n,i={},s,o)=>r=>{const a=ni(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=O(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||Object.assign(c,Po(t,c)),c.duration&&(c.duration=O(c.duration)),c.repeatDelay&&(c.repeatDelay=O(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&($n(c),0===c.delay&&(h=!0)),(S.instantAnimations||S.skipAnimations)&&(h=!0,$n(c),c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(vo),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}(c.keyframes,a);if(void 0!==t)return void at.update(()=>{c.onUpdate(t),c.onComplete()})}return a.isSync?new qe(c):new Yn(c)};function So(t,e,n){const i=Vs(t)?t:ji(t);return i.start(bo("",i,e,n)),i.animation}const Eo=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Ao="framerAppearId",Mo="data-"+Eo(Ao);function Co(t){return t.props[Mo]}const Vo=(t,e)=>t.depth-e.depth;class Ro{constructor(){this.children=[],this.isDirty=!1}add(t){x(this.children,t),this.isDirty=!0}remove(t){w(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Vo),this.isDirty=!1,this.children.forEach(t)}}function Do(t,e){const n=pt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(lt(i),t(o-e))};return at.setup(i,!0),()=>lt(i)}function ko(t){return Vs(t)?t.get():t}const Lo=["TopLeft","TopRight","BottomLeft","BottomRight"],Oo=Lo.length,jo=t=>"string"==typeof t?parseFloat(t):t,Bo=t=>"number"==typeof t||Ot.test(t);function Io(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Fo=Uo(0,.5,G),Wo=Uo(.5,.95,V);function Uo(t,e,n){return i=>i<t?0:i>e?1:n(k(t,e,i))}function No(t,e){t.min=e.min,t.max=e.max}function $o(t,e){No(t.x,e.x),No(t.y,e.y)}function zo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Xo(t){return void 0===t||1===t}function Ho({scale:t,scaleX:e,scaleY:n}){return!Xo(t)||!Xo(e)||!Xo(n)}function Yo(t){return Ho(t)||Ko(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Ko(t){return Go(t.x)||Go(t.y)}function Go(t){return t&&"0%"!==t}function _o(t,e,n){return n+e*(t-n)}function Zo(t,e,n,i,s){return void 0!==s&&(t=_o(t,s,i)),_o(t,n,i)+e}function qo(t,e=0,n=1,i,s){t.min=Zo(t.min,e,n,i,s),t.max=Zo(t.max,e,n,i,s)}function Jo(t,{x:e,y:n}){qo(t.x,e.translate,e.scale,e.originPoint),qo(t.y,n.translate,n.scale,n.originPoint)}const Qo=.999999999999,tr=1.0000000000001;function er(t,e){t.min=t.min+e,t.max=t.max+e}function nr(t,e,n,i,s=.5){qo(t,e,n,Jt(t.min,t.max,s),i)}function ir(t,e){nr(t.x,e.x,e.scaleX,e.scale,e.originX),nr(t.y,e.y,e.scaleY,e.scale,e.originY)}function sr(t,e,n,i,s){return t=_o(t-=e,1/n,i),void 0!==s&&(t=_o(t,1/s,i)),t}function or(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){Lt.test(e)&&(e=parseFloat(e),e=Jt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Jt(o.min,o.max,i);t===o&&(a-=e),t.min=sr(t.min,e,n,a,s),t.max=sr(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const rr=["x","scaleX","originX"],ar=["y","scaleY","originY"];function lr(t,e,n,i){or(t.x,e,rr,n?n.x:void 0,i?i.x:void 0),or(t.y,e,ar,n?n.y:void 0,i?i.y:void 0)}const ur=()=>({x:{min:0,max:0},y:{min:0,max:0}});function cr(t){return 0===t.translate&&1===t.scale}function hr(t){return cr(t.x)&&cr(t.y)}function dr(t,e){return t.min===e.min&&t.max===e.max}function pr(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function mr(t,e){return pr(t.x,e.x)&&pr(t.y,e.y)}function fr(t){return uo(t.x)/uo(t.y)}function gr(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class yr{constructor(){this.members=[]}add(t){x(this.members,t),t.scheduleRender()}remove(t){if(w(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const vr={};function xr(t){for(const e in t)vr[e]=t[e],gt(e)&&(vr[e].isCSSVariable=!0)}function wr(t){return[t("x"),t("y")]}const Tr={hasAnimatedSinceResize:!0,hasEverUpdated:!1},Pr={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},br=["","X","Y","Z"];let Sr=0;function Er(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function Ar(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Co(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",at,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&Ar(i)}function Mr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=Sr++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ot.value&&(Pr.nodes=Pr.calculatedTargetDeltas=Pr.calculatedProjections=0),this.nodes.forEach(Rr),this.nodes.forEach(Ir),this.nodes.forEach(Fr),this.nodes.forEach(Dr),ot.addProjectionMetrics&&ot.addProjectionMetrics(Pr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ro)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new L),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=os(e)&&!Es(e),this.instance=e;const{layoutId:n,layout:i,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||n)&&(this.isLayoutDirty=!0),t){let n,i=0;const s=()=>this.root.updateBlockedByResize=!1;at.read(()=>{i=window.innerWidth}),t(e,()=>{const t=window.innerWidth;t!==i&&(i=t,this.root.updateBlockedByResize=!0,n&&n(),n=Do(s,250),Tr.hasAnimatedSinceResize&&(Tr.hasAnimatedSinceResize=!1,this.nodes.forEach(Br)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||i)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||s.getDefaultTransition()||Xr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!mr(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...ni(o,"layout"),onPlay:r,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||Br(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),lt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Wr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ar(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Lr);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(Or);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(jr),this.nodes.forEach(Cr),this.nodes.forEach(Vr)):this.nodes.forEach(Or),this.clearAllSnapshots();const t=pt.now();ut.delta=P(0,1e3/60,t-ut.timestamp),ut.timestamp=t,ut.isProcessing=!0,ct.update.process(ut),ct.preRender.process(ut),ct.render.process(ut),ut.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,$i.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(kr),this.sharedNodes.forEach(Ur)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,at.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){at.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||uo(this.snapshot.measuredBox.x)||uo(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!hr(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||Yo(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Kr((i=n).x),Kr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(_r))){const{scroll:t}=this.root;t&&(er(e.x,t.offset.x),er(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if($o(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&$o(e,t),er(e.x,s.offset.x),er(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};$o(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ir(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),Yo(i.latestValues)&&ir(n,i.latestValues)}return Yo(this.latestValues)&&ir(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};$o(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!Yo(n.latestValues))continue;Ho(n.latestValues)&&n.updateSnapshot();const i=ur();$o(i,n.measurePageBox()),lr(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return Yo(this.latestValues)&&lr(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ut.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=ut.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},fo(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),$o(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,r,a;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,po(o.x,r.x,a.x),po(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):$o(this.target,this.layout.layoutBox),Jo(this.target,this.targetDelta)):$o(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},fo(this.relativeTargetOrigin,this.target,t.target),$o(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ot.value&&Pr.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Ho(this.parent.latestValues)&&!Ko(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===ut.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;$o(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ir(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Jo(t,r)),i&&Yo(o.latestValues)&&ir(t,o.latestValues))}e.x<tr&&e.x>Qo&&(e.x=1),e.y<tr&&e.y>Qo&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(zo(this.prevProjectionDelta.x,this.projectionDelta.x),zo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ho(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&gr(this.projectionDelta.x,this.prevProjectionDelta.x)&&gr(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ot.value&&Pr.calculatedProjections++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(zr));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;Nr(o.x,t.x,n),Nr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(fo(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){$r(t.x,e.x,n.x,i),$r(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,dr(l.x,d.x)&&dr(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),$o(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Jt(0,n.opacity??1,Fo(i)),t.opacityExit=Jt(e.opacity??1,0,Wo(i))):o&&(t.opacity=Jt(e.opacity??1,n.opacity??1,i));for(let s=0;s<Oo;s++){const o=`border${Lo[s]}Radius`;let r=Io(e,o),a=Io(n,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||Bo(r)===Bo(a)?(t[o]=Math.max(Jt(jo(r),jo(a),i),0),(Lt.test(a)||Lt.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=Jt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(lt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=at.update(()=>{Tr.hasAnimatedSinceResize=!0,mt.layout++,this.motionValue||(this.motionValue=ji(0)),this.currentAnimation=So(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{mt.layout--},onComplete:()=>{mt.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Gr(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=uo(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=uo(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}$o(e,n),ir(e,s),ho(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new yr);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&Er("z",t,i,this.animationValues);for(let e=0;e<br.length;e++)Er(`rotate${br[e]}`,t,i,this.animationValues),Er(`skew${br[e]}`,t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return void(t.visibility="hidden");const n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=ko(e?.pointerEvents)||"",void(t.transform=n?n(this.latestValues,""):"none");const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target)return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ko(e?.pointerEvents)||""),void(this.hasProjected&&!Yo(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1));t.visibility="";const s=i.animationValues||i.latestValues;this.applyTransformsToTarget();let o=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);n&&(o=n(s,o)),t.transform=o;const{x:r,y:a}=this.projectionDelta;t.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,i.animationValues?t.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const e in vr){if(void 0===s[e])continue;const{correct:n,applyTo:r,isCSSVariable:a}=vr[e],l="none"===o?s[e]:n(s[e],i);if(r){const e=r.length;for(let n=0;n<e;n++)t[r[n]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=i===this?ko(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(Lr),this.root.sharedNodes.clear()}}}function Cr(t){t.updateLayout()}function Vr(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?wr(t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=uo(i);i.min=n[t].min,i.max=i.min+s}):Gr(s,e.layoutBox,n)&&wr(i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=uo(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};ho(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?ho(a,t.applyTransform(i,!0),e.measuredBox):ho(a,n,e.layoutBox);const l=!hr(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};fo(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};fo(a,n,o.layoutBox),mr(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function Rr(t){ot.value&&Pr.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Dr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function kr(t){t.clearSnapshot()}function Lr(t){t.clearMeasurements()}function Or(t){t.isLayoutDirty=!1}function jr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Br(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Ir(t){t.resolveTargetDelta()}function Fr(t){t.calcProjection()}function Wr(t){t.resetSkewAndRotation()}function Ur(t){t.removeLeadSnapshot()}function Nr(t,e,n){t.translate=Jt(e.translate,0,n),t.scale=Jt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function $r(t,e,n,i){t.min=Jt(e.min,n.min,i),t.max=Jt(e.max,n.max,i)}function zr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Xr={duration:.45,ease:[.4,0,.1,1]},Hr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Yr=Hr("applewebkit/")&&!Hr("chrome/")?Math.round:V;function Kr(t){t.min=Yr(t.min),t.max=Yr(t.max)}function Gr(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=fr(e),s=fr(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function _r(t){return t!==t.root&&t.scroll?.wasRoot}function Zr(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const qr=Mr({attachResizeListener:(t,e)=>Zr(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Jr={current:void 0},Qr=Mr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Jr.current){const t=new qr({});t.mount(window),t.setOptions({layoutScroll:!0}),Jr.current=t}return Jr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function ta(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const ea={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Ot.test(t))return t;t=parseFloat(t)}return`${ta(t,e.target.x)}% ${ta(t,e.target.y)}%`}},na={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Gt.parse(t);if(s.length>5)return i;const o=Gt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Jt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};function ia({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function sa(t,e){return ia(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const oa={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ra={};for(const t in oa)ra[t]={isEnabled:e=>oa[t].some(t=>!!e[t])};const aa={current:null},la={current:!1};function ua(){if(la.current=!0,g)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>aa.current=t.matches;t.addEventListener("change",e),e()}else aa.current=!1}const ca=new WeakMap;function ha(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function da(t){return"string"==typeof t||Array.isArray(t)}const pa=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ma=["initial",...pa];function fa(t){return ha(t.animate)||ma.some(e=>da(t[e]))}function ga(t){return Boolean(fa(t)||t.variants)}function ya(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function va(t,e,n,i){if("function"==typeof e){const[s,o]=ya(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=ya(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const xa=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class wa{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Sn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=pt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,at.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=fa(e),this.isVariantNode=ga(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&Vs(e)&&e.set(a[t])}}mount(t){this.current=t,ca.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),la.current||ua(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||aa.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),lt(this.notifyUpdate),lt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=dn.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&at.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s&&s(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in ra){const e=ra[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<xa.length;e++){const n=xa[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(Vs(s))t.addValue(i,s);else if(Vs(o))t.addValue(i,ji(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,ji(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=ji(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(E(n)||M(n))?n=parseFloat(n):!Os(n)&&Gt.test(e)&&(n=yi(t,e)),this.setBaseTarget(t,Vs(n)?n.get():n)),Vs(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=va(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||Vs(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new L),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){$i.render(this.render)}}class Ta extends wa{constructor(){super(...arguments),this.KeyframeResolver=xi}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Vs(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}const Pa={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ba=hn.length;function Sa(t,e,n){let i="",s=!0;for(let o=0;o<ba;o++){const r=hn[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Ai(a,mi[r]);if(!l){s=!1;i+=`${Pa[r]||r}(${t}) `}n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function Ea(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(dn.has(t))r=!0;else if(gt(t))s[t]=n;else{const e=Ai(n,mi[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=Sa(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}function Aa(t,{style:e,vars:n},i,s){const o=t.style;let r;for(r in e)o[r]=e[r];for(r in s?.applyProjectionStyles(o,i),n)o.setProperty(r,n[r])}function Ma(t,{layout:e,layoutId:n}){return dn.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!vr[t]||"opacity"===t)}function Ca(t,e,n){const{style:i}=t,s={};for(const o in i)(Vs(i[o])||e.style&&Vs(e.style[o])||Ma(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}class Va extends Ta{constructor(){super(...arguments),this.type="html",this.renderInstance=Aa}readValueFromInstance(t,e){if(dn.has(e))return this.projection?.isProjecting?an(e):un(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(gt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return sa(t,e)}build(t,e,n){Ea(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Ca(t,e,n)}}function Ra(){const t=function(){const t=e.useRef(!1);return y(()=>(t.current=!0,()=>{t.current=!1}),[]),t}(),[n,i]=e.useState(0),s=e.useCallback(()=>{t.current&&i(n+1)},[n]);return[e.useCallback(()=>at.postRender(s),[s]),n]}const Da=t=>!0===t,ka=({children:t,id:n,inherit:i=!0})=>{const s=e.useContext(m),o=e.useContext(lo),[r,a]=Ra(),l=e.useRef(null),u=s.id||o;null===l.current&&((t=>Da(!0===t)||"id"===t)(i)&&u&&(n=n?u+"-"+n:u),l.current={id:n,group:Da(i)&&s.group||yo()});const c=e.useMemo(()=>({...l.current,forceRender:r}),[a]);return d(m.Provider,{value:c,children:t})},La=e.createContext({strict:!1});function Oa(t){for(const e in t)ra[e]={...ra[e],...t[e]}}function ja(t){return"function"==typeof t}const Ba=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ia(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Ba.has(t)}let Fa=t=>!Ia(t);function Wa(t){"function"==typeof t&&(Fa=e=>e.startsWith("on")?!Ia(e):t(e))}try{Wa(require("@emotion/is-prop-valid").default)}catch{}function Ua(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(Fa(s)||!0===n&&Ia(s)||!e&&!Ia(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}const Na=e.createContext(null),$a={offset:"stroke-dashoffset",array:"stroke-dasharray"},za={offset:"strokeDashoffset",array:"strokeDasharray"};function Xa(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,c){if(Ea(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==n&&(h.y=n),void 0!==i&&(h.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?$a:za;t[o.offset]=Ot.transform(-i);const r=Ot.transform(e),a=Ot.transform(n);t[o.array]=`${r} ${a}`}(h,s,o,r,!1)}const Ha=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),Ya=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Ka(t,e,n){const i=Ca(t,e,n);for(const n in t)if(Vs(t[n])||Vs(e[n])){i[-1!==hn.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}class Ga extends Ta{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ur}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(dn.has(e)){const t=gi(e);return t&&t.default||0}return e=Ha.has(e)?e:Eo(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return Ka(t,e,n)}build(t,e,n){Xa(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){Aa(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(Ha.has(n)?n:Eo(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=Ya(t.tagName),super.mount(t)}}const _a=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Za(t){return"string"==typeof t&&!t.includes("-")&&!!(_a.indexOf(t)>-1||/[A-Z]/u.test(t))}const qa=(t,n)=>Za(t)?new Ga(n):new Va(n,{allowProjection:t!==e.Fragment}),Ja=e.createContext({});function Qa(t){const{initial:n,animate:i}=function(t,e){if(fa(t)){const{initial:e,animate:n}=t;return{initial:!1===e||da(e)?e:void 0,animate:da(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(Ja));return e.useMemo(()=>({initial:n,animate:i}),[tl(n),tl(i)])}function tl(t){return Array.isArray(t)?t.join(" "):t}const el=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nl(t,e,n){for(const i in e)Vs(e[i])||Ma(i,n)||(t[i]=e[i])}function il(t,n){const i={};return nl(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Ea(e,n,t),Object.assign({},e.vars,e.style)},[n])}(t,n)),i}function sl(t,e){const n={},i=il(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const ol=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});function rl(t,n,i,s){const o=e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Xa(e,n,Ya(s),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}},[n]);if(t.style){const e={};nl(e,t.style,t),o.style={...e,...o.style}}return o}function al(t,n,i,{latestValues:s},o,r=!1){const a=(Za(t)?rl:sl)(n,s,o,t),l=Ua(n,"string"==typeof t,r),u=t!==e.Fragment?{...l,...a,ref:i}:{},{children:c}=n,h=e.useMemo(()=>Vs(c)?c.get():c,[c]);return e.createElement(t,{...u,children:h})}function ll(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=ko(o[t]);let{initial:r,animate:a}=t;const l=fa(t),u=ga(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!ha(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=va(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const ul=t=>(n,i)=>{const s=e.useContext(Ja),o=e.useContext(v),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:ll(n,i,s,t),renderState:e()}}(t,n,s,o);return i?r():f(r)},cl=ul({scrapeMotionValuesFromProps:Ca,createRenderState:el}),hl=ul({scrapeMotionValuesFromProps:Ka,createRenderState:ol}),dl=Symbol.for("motionComponentSymbol");function pl(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function ml(t,n,i){return e.useCallback(e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):pl(i)&&(i.current=e))},[n])}const fl=e.createContext({});function gl(t,n,i,s,o){const{visualElement:r}=e.useContext(Ja),a=e.useContext(La),l=e.useContext(v),u=e.useContext(to).reducedMotion,c=e.useRef(null);s=s||a.renderer,!c.current&&s&&(c.current=s(t,{visualState:n,parent:r,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));const h=c.current,d=e.useContext(fl);!h||h.projection||!o||"html"!==h.type&&"svg"!==h.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:yl(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&pl(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,i,o,d);const p=e.useRef(!1);e.useInsertionEffect(()=>{h&&p.current&&h.update(i,l)});const m=i[Mo],f=e.useRef(Boolean(m)&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return y(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),h.scheduleRenderMicrotask(),f.current&&h.animationState&&h.animationState.animateChanges())}),e.useEffect(()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1),h.enteringChildren=void 0)}),h}function yl(t){if(t)return!1!==t.options.allowProjection?t.projection:yl(t.parent)}function vl(t,{forwardMotionProps:n=!1}={},i,s){i&&Oa(i);const o=Za(t)?hl:cl;function r(i,r){let a;const l={...e.useContext(to),...i,layoutId:xl(i)},{isStatic:u}=l,c=Qa(i),h=o(i,u);if(!u&&g){e.useContext(La).strict;const n=function(t){const{drag:e,layout:n}=ra;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=n.MeasureLayout,c.visualElement=gl(t,h,l,s,n.ProjectionNode)}return p(Ja.Provider,{value:c,children:[a&&c.visualElement?d(a,{visualElement:c.visualElement,...l}):null,al(t,i,ml(h,c.visualElement,r),h,u,n)]})}r.displayName=`motion.${"string"==typeof t?t:`create(${t.displayName??t.name??""})`}`;const a=e.forwardRef(r);return a[dl]=t,a}function xl({layoutId:t}){const n=e.useContext(m).id;return n&&void 0!==t?n+"-"+t:t}function wl(t,e){if("undefined"==typeof Proxy)return vl;const n=new Map,i=(n,i)=>vl(n,i,t,e);return new Proxy((t,e)=>i(t,e),{get:(s,o)=>"create"===o?i:(n.has(o)||n.set(o,vl(o,void 0,t,e)),n.get(o))})}function Tl(t,e,n){const i=t.getProps();return va(i,e,void 0!==n?n:i.custom,t)}const Pl=t=>Array.isArray(t);function bl(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ji(n))}function Sl(t){return Pl(t)?t[t.length-1]||0:t}function El(t,e){const n=Tl(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o){bl(t,e,Sl(o[e]))}}function Al(t,e){const n=t.getValue("willChange");if(i=n,Boolean(Vs(i)&&i.add))return n.add(e);if(!n&&S.WillChange){const n=new S.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function Ml({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function Cl(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||u&&Ml(u,e))continue;const r={delay:n,...ni(o||{},e)},c=i.get();if(void 0!==c&&!i.isAnimating&&!Array.isArray(s)&&s===c&&!r.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const n=Co(t);if(n){const t=window.MotionHandoffAnimation(n,e,at);null!==t&&(r.startTime=t,h=!0)}}Al(t,e),i.start(bo(e,i,s,t.shouldReduceMotion&&ii.has(e)?{type:!1}:r,t,h));const d=i.animation;d&&l.push(d)}return r&&Promise.all(l).then(()=>{at.update(()=>{r&&El(t,r)})}),l}function Vl(t,e,n,i=0,s=1){const o=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),r=t.size,a=(r-1)*i;return"function"==typeof n?n(o,r):1===s?o*i:a-o*i}function Rl(t,e,n={}){const i=Tl(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Cl(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=0,o=1,r){const a=[];for(const l of t.variantChildren)l.notify("AnimationStart",e),a.push(Rl(l,e,{...r,delay:n+("function"==typeof i?0:i)+Vl(t.variantChildren,l,i,s,o)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,i,o,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(n.delay)])}function Dl(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>Rl(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=Rl(t,e,n);else{const s="function"==typeof e?Tl(t,e,n.custom):e;i=Promise.all(Cl(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}function kl(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const Ll=ma.length;function Ol(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Ol(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Ll;n++){const i=ma[n],s=t.props[i];(da(s)||!1===s)&&(e[i]=s)}return e}const jl=[...pa].reverse(),Bl=pa.length;function Il(t){let e=function(t){return e=>Promise.all(e.map(({animation:e,options:n})=>Dl(t,e,n)))}(t),n=Ul(),i=!0;const s=e=>(n,i)=>{const s=Tl(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=Ol(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<Bl;e++){const d=jl[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=da(m),g=d===o?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||ha(m)||"boolean"==typeof m)continue;const v=Fl(p.prevProp,m);let x=v||d===o&&p.isActive&&!y&&f||e>h&&f,w=!1;const T=Array.isArray(m)?m:[m];let P=T.reduce(s(d),{});!1===g&&(P={});const{prevResolvedValues:b={}}=p,S={...b,...P},E=e=>{x=!0,u.has(e)&&(w=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in S){const e=P[t],n=b[t];if(c.hasOwnProperty(t))continue;let i=!1;i=Pl(e)&&Pl(n)?!kl(e,n):e!==n,i?null!=e?E(t):u.add(t):void 0!==e&&u.has(t)?E(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(c={...c,...P}),i&&t.blockInitialAnimation&&(x=!1);const A=y&&v;x&&(!A||w)&&l.push(...T.map(e=>{const n={type:d};if("string"==typeof e&&i&&!A&&t.manuallyAnimateOnMount&&t.parent){const{parent:i}=t,s=Tl(i,e);if(i.enteringChildren&&s){const{delayChildren:e}=s.transition||{};n.delay=Vl(i.enteringChildren,t,e)}}return{animation:e,options:n}}))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=Tl(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Ul(),i=!0}}}function Fl(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!kl(e,t)}function Wl(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ul(){return{animate:Wl(!0),whileInView:Wl(),whileHover:Wl(),whileTap:Wl(),whileDrag:Wl(),whileFocus:Wl(),exit:Wl()}}class Nl{constructor(t){this.isMounted=!1,this.node=t}update(){}}let $l=0;const zl={animation:{Feature:class extends Nl{constructor(t){super(t),t.animationState||(t.animationState=Il(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();ha(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Nl{constructor(){super(...arguments),this.id=$l++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Xl(t){return{point:{x:t.pageX,y:t.pageY}}}const Hl=t=>e=>qi(e)&&t(e,Xl(e));function Yl(t,e,n,i){return Zr(t,e,Hl(n),i)}const Kl=({current:t})=>t?t.ownerDocument.defaultView:null,Gl=(t,e)=>Math.abs(t-e);function _l(t,e){const n=Gl(t.x,e.x),i=Gl(t.y,e.y);return Math.sqrt(n**2+i**2)}class Zl{constructor(t,e,{transformPagePoint:n,contextWindow:i=window,dragSnapToOrigin:s=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Ql(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=_l(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=ut;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ql(e,this.transformPagePoint),at.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Ql("pointercancel"===t.type?this.lastMoveEventInfo:ql(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!qi(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=i||window;const r=ql(Xl(t),this.transformPagePoint),{point:a}=r,{timestamp:l}=ut;this.history=[{...a,timestamp:l}];const{onSessionStart:u}=e;u&&u(t,Ql(r,this.history)),this.removeListeners=D(Yl(this.contextWindow,"pointermove",this.handlePointerMove),Yl(this.contextWindow,"pointerup",this.handlePointerUp),Yl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),lt(this.updatePoint)}}function ql(t,e){return e?{point:e(t.point)}:t}function Jl(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ql({point:t},e){return{point:t,delta:Jl(t,eu(e)),offset:Jl(t,tu(e)),velocity:nu(e,.1)}}function tu(t){return t[0]}function eu(t){return t[t.length-1]}function nu(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=eu(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>O(e)));)n--;if(!i)return{x:0,y:0};const o=j(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function iu(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function su(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const ou=.35;function ru(t,e,n){return{min:au(t,e),max:au(t,n)}}function au(t,e){return"number"==typeof t?t:t[e]||0}const lu=new WeakMap;class uu{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:n}={}){const{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;const{dragSnapToOrigin:s}=this.getProps();this.panSession=new Zl(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Xl(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Yi(n),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),wr(t=>{let e=this.getAxisMotionValue(t).get()||0;if(Lt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=uo(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&at.postRender(()=>s(t,e)),Al(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>wr(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:n,contextWindow:Kl(this.visualElement)})}stop(t,e){const n=t||this.latestPointerEvent,i=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!i||!n)return;const{velocity:o}=i;this.startAnimation(o);const{onDragEnd:r}=this.getProps();r&&at.postRender(()=>r(n,i))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!cu(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Jt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Jt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&pl(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:iu(t.x,n,s),y:iu(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=ou){return!1===t?t=0:!0===t&&(t=ou),{x:ru(t,"left","right"),y:ru(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&wr(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!pl(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=sa(t,n),{scroll:s}=e;return s&&(er(i.x,s.offset.x),er(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:su(t.x,e.x),y:su(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ia(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=wr(r=>{if(!cu(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return Al(this.visualElement,t),n.start(bo(t,n,0,e,this.visualElement,!1))}stopAnimation(){wr(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){wr(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){wr(e=>{const{drag:n}=this.getProps();if(!cu(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Jt(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!pl(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};wr(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=uo(t),s=uo(e);return s>i?n=k(e.min,e.max-i,t.min):i>s&&(n=k(t.min,t.max-s,e.min)),P(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),wr(e=>{if(!cu(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Jt(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;lu.set(this.visualElement,this);const t=Yl(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();pl(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),at.read(e);const s=Zr(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(wr(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=ou,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function cu(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const hu=t=>(e,n)=>{t&&at.postRender(()=>t(e,n))};let du=!1;class pu extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;xr(fu),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),du&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Tr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,du=!0,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||at.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),$i.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;du=!0,i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function mu(t){const[n,i]=oo(),s=e.useContext(m);return d(pu,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(fl),isPresent:n,safeToRemove:i})}const fu={borderRadius:{...ea,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ea,borderTopRightRadius:ea,borderBottomLeftRadius:ea,borderBottomRightRadius:ea,boxShadow:na},gu={pan:{Feature:class extends Nl{constructor(){super(...arguments),this.removePointerDownListener=V}onPointerDown(t){this.session=new Zl(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Kl(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:hu(t),onStart:hu(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&at.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=Yl(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Nl{constructor(t){super(t),this.removeGroupControls=V,this.removeListeners=V,this.controls=new uu(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||V}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Qr,MeasureLayout:mu}};function yu(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&at.postRender(()=>s(e,Xl(e)))}function vu(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&at.postRender(()=>s(e,Xl(e)))}const xu=new WeakMap,wu=new WeakMap,Tu=t=>{const e=xu.get(t.target);e&&e(t)},Pu=t=>{t.forEach(Tu)};function bu(t,e,n){const i=function({root:t,...e}){const n=t||document;wu.has(n)||wu.set(n,{});const i=wu.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Pu,{root:t,...e})),i[s]}(e);return xu.set(t,n),i.observe(t),()=>{xu.delete(t),i.unobserve(t)}}const Su={some:0,all:1};const Eu={inView:{Feature:class extends Nl{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:Su[i]};return bu(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Nl{mount(){const{current:t}=this.node;t&&(this.unmount=is(t,(t,e)=>(vu(this.node,e,"Start"),(t,{success:e})=>vu(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Nl{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=D(Zr(this.node.current,"focus",()=>this.onFocus()),Zr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Nl{mount(){const{current:t}=this.node;t&&(this.unmount=_i(t,(t,e)=>(yu(this.node,e,"Start"),t=>yu(this.node,t,"End"))))}unmount(){}}}},Au={layout:{ProjectionNode:Qr,MeasureLayout:mu}},Mu=wl({...zl,...Eu,...gu,...Au},qa);function Cu({children:t,as:n="ul",axis:i="y",onReorder:s,values:o,...r},a){const l=f(()=>Mu[n]),u=[],c=e.useRef(!1),h={axis:i,registerItem:(t,e)=>{const n=u.findIndex(e=>t===e.value);-1!==n?u[n].layout=e[i]:u.push({value:t,layout:e[i]}),u.sort(Du)},updateOrder:(t,e,n)=>{if(c.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex(t=>t.value===e);if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=Jt(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?T(t,s,s+o):t}(u,t,e,n);u!==i&&(c.current=!0,s(i.map(Ru).filter(t=>-1!==o.indexOf(t))))}};return e.useEffect(()=>{c.current=!1}),d(l,{...r,ref:a,ignoreStrict:!0,children:d(Na.Provider,{value:h,children:t})})}const Vu=e.forwardRef(Cu);function Ru(t){return t.value}function Du(t,e){return t.layout.min-e.layout.min}function ku(t){const n=f(()=>ji(t)),{isStatic:i}=e.useContext(to);if(i){const[,i]=e.useState(t);e.useEffect(()=>n.on("change",i),[])}return n}function Lu(t,e){const n=ku(e()),i=()=>n.set(e());return i(),y(()=>{const e=()=>at.preRender(i,!1,!0),n=t.map(t=>t.on("change",e));return()=>{n.forEach(t=>t()),lt(i)}}),n}function Ou(t,e,n,i){if("function"==typeof t)return function(t){Li.current=[],t();const e=Lu(Li.current,t);return Li.current=void 0,e}(t);const s="function"==typeof e?e:Ms(e,n,i);return Array.isArray(t)?ju(t,s):ju([t],([t])=>s(t))}function ju(t,e){const n=f(()=>[]);return Lu(t,()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)})}function Bu(t,e=0){return Vs(t)?t:ku(e)}function Iu({children:t,style:n={},value:i,as:s="li",onDrag:o,layout:r=!0,...a},l){const u=f(()=>Mu[s]),c=e.useContext(Na),h={x:Bu(n.x),y:Bu(n.y)},p=Ou([h.x,h.y],([t,e])=>t||e?1:"unset"),{axis:m,registerItem:g,updateOrder:y}=c;return d(u,{drag:m,...a,dragSnapToOrigin:!0,style:{...n,x:h.x,y:h.y,zIndex:p},layout:r,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&y(i,h[m].get(),n[m]),o&&o(t,e)},onLayoutMeasure:t=>g(i,t),ref:l,ignoreStrict:!0,children:t})}const Fu=e.forwardRef(Iu);var Wu=Object.freeze({__proto__:null,Group:Vu,Item:Fu});function Uu(t){return"object"==typeof t&&!Array.isArray(t)}function Nu(t,e,n,i){return"string"==typeof t&&Uu(e)?Si(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function $u(t,e,n){return t*(e+1)}function zu(t,e,n,i){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:e.startsWith("<")?Math.max(0,n+parseFloat(e.slice(1))):i.get(e)??t}function Xu(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(w(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:Jt(s,o,i[r]),easing:tt(n,r)})}function Hu(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Yu(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function Ku(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function Gu(t,e){return e[t]||(e[t]=[]),e[t]}function _u(t){return Array.isArray(t)?t:[t]}function Zu(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const qu=t=>"number"==typeof t,Ju=t=>t.every(qu);class Qu extends wa{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}function tc(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=os(t)&&!Es(t)?new Ga(e):new Va(e);n.mount(t),ca.set(t,n)}function ec(t){const e=new Qu({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),ca.set(t,e)}function nc(t,e,n,i){const s=[];if(function(t,e){return Vs(t)||"number"==typeof t||"string"==typeof t&&!Uu(e)}(t,e))s.push(So(t,Uu(e)&&e.default||e,n&&n.default||n));else{const o=Nu(t,e,i),r=o.length;for(let t=0;t<r;t++){const i=o[t],a=i instanceof Element?tc:ec;ca.has(i)||a(i);const l=ca.get(i),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,r)),s.push(...Cl(l,{...e,transition:u},{}))}}return s}function ic(t,e,n){const i=[],s=function(t,{defaultTransition:e={},...n}={},i,s){const o=e.duration||.3,r=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const r=t[n];if("string"==typeof r){u.set(r,h);continue}if(!Array.isArray(r)){u.set(r.name,zu(h,r.at,c,u));continue}let[p,m,f={}]=r;void 0!==f.at&&(h=zu(h,f.at,c,u));let g=0;const y=(t,n,i,r=0,a=0)=>{const l=_u(t),{delay:u=0,times:c=Ne(l),type:p="keyframes",repeat:m,repeatType:f,repeatDelay:y=0,...v}=n;let{ease:x=e.ease||"easeOut",duration:w}=n;const T="function"==typeof u?u(r,a):u,P=l.length,b=jn(p)?p:s?.[p||"keyframes"];if(P<=2&&b){let t=100;if(2===P&&Ju(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...v};void 0!==w&&(e.duration=O(w));const n=fe(e,t,b);x=n.ease,w=n.duration}w??(w=o);const S=h+T;1===c.length&&0===c[0]&&(c[1]=1);const E=c.length-l.length;if(E>0&&Ue(c,E),1===l.length&&l.unshift(null),m){w=$u(w,m);const t=[...l],e=[...c];x=Array.isArray(x)?[...x]:[x];const n=[...x];for(let i=0;i<m;i++){l.push(...t);for(let s=0;s<t.length;s++)c.push(e[s]+(i+1)),x.push(0===s?"linear":tt(n,s-1))}Hu(c,m)}const A=S+w;Xu(i,l,x,c,S,A),g=Math.max(T+w,g),d=Math.max(A,d)};if(Vs(p))y(m,f,Gu("default",Ku(p,a)));else{const t=Nu(p,m,i,l),e=t.length;for(let n=0;n<e;n++){const i=Ku(t[n],a);for(const t in m)y(m[t],Zu(f,t),Gu(t,i),n,e)}}c=h,h+=g}return a.forEach((t,i)=>{for(const s in t){const o=t[s];o.sort(Yu);const a=[],l=[],u=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:i}=o[t];a.push(n),l.push(k(0,d,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),r.has(i)||r.set(i,{keyframes:{},transition:{}});const c=r.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:d,ease:u,times:l,...n}}}),r}(t,e,n,{spring:Ie});return s.forEach(({keyframes:t,transition:e},n)=>{i.push(...nc(n,t,e))}),i}function sc(t){return function(e,n,i){let s=[];var o;o=e,s=Array.isArray(o)&&o.some(Array.isArray)?ic(e,n,t):nc(e,n,i,t);const r=new Gn(s);return t&&(t.animations.push(r),r.finished.then(()=>{w(t.animations,r)})),r}}const oc=sc();const rc=t=>function(e,n,i){return new Gn(function(t,e,n,i){const s=Si(t,i),o=s.length,r=[];for(let t=0;t<o;t++){const i=s[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,o));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const s={...ni(a,t)};s.duration&&(s.duration=O(s.duration)),s.delay&&(s.delay=O(s.delay));const o=Jn(i),l=qn(t,s.pseudoElement||""),u=o.get(l);u&&u.stop(),r.push({map:o,key:l,unresolvedKeyframes:n,options:{...s,element:i,name:t,allowFlatten:!a.type&&!a.ease}})}}for(let t=0;t<r.length;t++){const{unresolvedKeyframes:e,options:n}=r[t],{element:i,name:s,pseudoElement:o}=n;o||null!==e[0]||(e[0]=ss(i,s)),Je(e),Ti(e,s),!o&&e.length<2&&e.unshift(ss(i,s)),n.keyframes=e}const a=[];for(let t=0;t<r.length;t++){const{map:e,key:n,options:i}=r[t],s=new In(i);e.set(n,s),s.finished.finally(()=>e.delete(n)),a.push(s)}return a}(e,n,i,t))},ac=rc(),lc={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function uc(t,e,n,i){const s=n[e],{length:o,position:r}=lc[e],a=s.current,l=n.time;s.current=t[`scroll${r}`],s.scrollLength=t[`scroll${o}`]-t[`client${o}`],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=k(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:B(s.current-a,u)}const cc={start:0,center:.5,end:1};function hc(t,e,n=0){let i=0;if(t in cc&&(t=cc[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const dc=[0,0];function pc(t,e,n,i){let s=Array.isArray(t)?t:dc,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,cc[t]?t:"0"]),o=hc(s[0],n,i),r=hc(s[1],e),o-r}const mc={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},fc={x:0,y:0};function gc(t,e,n){const{offset:i=mc.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(ki(i))n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):fc,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=pc(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=We(e[o].offset,Ne(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=P(0,1,e[o].interpolate(e[o].current))}function yc(t,e,n,i={}){return{measure:e=>{!function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),function(t,e,n){uc(t,"x",e,n),uc(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&gc(t,n,i)},notify:()=>e(n)}}const vc=new WeakMap,xc=new WeakMap,wc=new WeakMap,Tc=t=>t===document.scrollingElement?window:t;function Pc(t,{container:e=document.scrollingElement,...n}={}){if(!e)return V;let i=wc.get(e);i||(i=new Set,wc.set(e,i));const s=yc(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!vc.has(e)){const t=()=>{for(const t of i)t.measure(ut.timestamp);at.preUpdate(n)},n=()=>{for(const t of i)t.notify()},s=()=>at.read(t);vc.set(e,s);const o=Tc(e);window.addEventListener("resize",s,{passive:!0}),e!==document.documentElement&&xc.set(e,ys(e,s)),o.addEventListener("scroll",s,{passive:!0}),s()}const o=vc.get(e);return at.read(o,!1,!0),()=>{lt(o);const t=wc.get(e);if(!t)return;if(t.delete(s),t.size)return;const n=vc.get(e);vc.delete(e),n&&(Tc(e).removeEventListener("scroll",n),xc.get(e)?.(),window.removeEventListener("resize",n))}}const bc=new Map;function Sc({source:t,container:e,...n}){const{axis:i}=n;t&&(e=t);const s=bc.get(e)??new Map;bc.set(e,s);const o=n.target??"self",r=s.get(o)??{},a=i+(n.offset??[]).join(",");return r[a]||(r[a]=!n.target&&Mn()?new ScrollTimeline({source:e,axis:i}):function(t){const e={value:0},n=Pc(n=>{e.value=100*n[t.axis].progress},t);return{currentTime:e,cancel:n}}({container:e,...n})),r[a]}function Ec(t,{axis:e="y",container:n=document.scrollingElement,...i}={}){if(!n)return V;const s={axis:e,container:n,...i};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?Pc(n=>{t(n[e.axis].progress,n)},e):vs(t,Sc(e))}(t,s):function(t,e){const n=Sc(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),vs(e=>{t.time=t.duration*e},n))})}(t,s)}const Ac={some:0,all:1};function Mc(t,e,{root:n,margin:i,amount:s="some"}={}){const o=Si(t),r=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))})},{root:n,rootMargin:i,threshold:"number"==typeof s?s:Ac[s]});return o.forEach(t=>a.observe(t)),()=>a.disconnect()}const Cc=wl();function Vc(t){return e.useEffect(()=>()=>t(),[])}const Rc={renderer:qa,...zl,...Eu},Dc={...Rc,...gu,...Au},kc={renderer:qa,...zl};function Lc(t,n,i){e.useInsertionEffect(()=>t.on(n,i),[t,n,i])}const Oc=()=>({scrollX:ji(0),scrollY:ji(0),scrollXProgress:ji(0),scrollYProgress:ji(0)}),jc=t=>!!t&&!t.current;function Bc({container:t,target:n,...i}={}){const s=f(Oc),o=e.useRef(null),r=e.useRef(!1),a=e.useCallback(()=>(o.current=Ec((t,{x:e,y:n})=>{s.scrollX.set(e.current),s.scrollXProgress.set(e.progress),s.scrollY.set(n.current),s.scrollYProgress.set(n.progress)},{...i,container:t?.current||void 0,target:n?.current||void 0}),()=>{o.current?.()}),[t,n,JSON.stringify(i.offset)]);return y(()=>(r.current=!1,jc(t)||jc(n)?void(r.current=!0):a()),[a]),e.useEffect(()=>r.current?(jc(t),jc(n),a()):void 0,[a]),s}function Ic(t){const n=e.useRef(0),{isStatic:i}=e.useContext(to);e.useEffect(()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return at.update(e,!0),()=>lt(e)},[t])}class Fc extends Oi{constructor(){super(...arguments),this.isEnabled=!1}add(t){(dn.has(t)||bi.has(t))&&(this.isEnabled=!0,this.update())}update(){this.set(this.isEnabled?"transform":"auto")}}function Wc(){!la.current&&ua();const[t]=e.useState(aa.current);return t}function Uc(t,e){[...e].reverse().forEach(n=>{const i=t.getVariant(n);i&&El(t,i),t.variantChildren&&t.variantChildren.forEach(t=>{Uc(t,e)})})}function Nc(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach(t=>{i.push(Dl(t,e,{transitionOverride:n}))}),Promise.all(i)},set:e=>t.forEach(t=>{!function(t,e){Array.isArray(e)?Uc(t,e):"string"==typeof e?Uc(t,[e]):El(t,e)}(t,e)}),stop(){t.forEach(t=>{!function(t){t.values.forEach(t=>t.stop())}(t)})},mount:()=>()=>{e.stop()}};return e}function $c(){const t=f(Nc);return y(t.mount,[]),t}const zc=$c;class Xc{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}cancel(){this.componentControls.forEach(t=>{t.cancel()})}stop(){this.componentControls.forEach(t=>{t.stop()})}}const Hc=()=>new Xc;function Yc(t){return null!==t&&"object"==typeof t&&dl in t}function Kc(){return Gc}function Gc(t){Jr.current&&(Jr.current.isUpdating=!1,Jr.current.blockUpdate(),t&&t())}const _c=new Map,Zc=new Map,qc=(t,e)=>`${t}: ${dn.has(e)?"transform":e}`;function Jc(t,e,n){const i=qc(t,e),s=_c.get(i);if(!s)return null;const{animation:o,startTime:r}=s;function a(){window.MotionCancelOptimisedAnimation?.(t,e,n)}return o.onfinish=a,null===r||window.MotionHandoffIsComplete?.(t)?(a(),null):r}let Qc,th;const eh=new Set;function nh(){eh.forEach(t=>{t.animation.play(),t.animation.startTime=t.startTime}),eh.clear()}const ih=()=>({});class sh extends wa{constructor(){super(...arguments),this.measureInstanceViewportBox=ur}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}}const oh=ul({scrapeMotionValuesFromProps:ih,createRenderState:ih});let rh=0;const ah=t=>t>.001?1/t:1e5;t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:r="sync",propagate:a=!1,anchorX:l="left",root:u})=>{const[c,p]=oo(a),g=e.useMemo(()=>ao(t),[t]),v=a&&!c?[]:g.map(ro),x=e.useRef(!0),w=e.useRef(g),T=f(()=>new Map),[P,b]=e.useState(g),[S,E]=e.useState(g);y(()=>{x.current=!1,w.current=g;for(let t=0;t<S.length;t++){const e=ro(S[t]);v.includes(e)?T.delete(e):!0!==T.get(e)&&T.set(e,!1)}},[S,v.length,v.join("-")]);const A=[];if(g!==P){let t=[...g];for(let e=0;e<S.length;e++){const n=S[e],i=ro(n);v.includes(i)||(t.splice(e,0,n),A.push(n))}return"wait"===r&&A.length&&(t=A),E(ao(t)),b(g),null}const{forceRender:M}=e.useContext(m);return d(h,{children:S.map(t=>{const e=ro(t),h=!(a&&!c)&&(g===S||v.includes(e));return d(io,{isPresent:h,initial:!(x.current&&!i)&&void 0,custom:n,presenceAffectsLayout:o,mode:r,root:u,onExitComplete:h?void 0:()=>{if(!T.has(e))return;T.set(e,!0);let t=!0;T.forEach(e=>{e||(t=!1)}),t&&(M?.(),E(w.current),a&&p?.(),s&&s())},anchorX:l,children:t},e)})})},t.AnimateSharedLayout=({children:t})=>(i.useEffect(()=>{},[]),d(ka,{id:f(()=>"asl-"+rh++),children:t})),t.AsyncMotionValueAnimation=Yn,t.DOMKeyframesResolver=xi,t.DeprecatedLayoutGroupContext=lo,t.DragControls=Xc,t.FlatTree=Ro,t.GroupAnimation=Kn,t.GroupAnimationWithThen=Gn,t.JSAnimation=qe,t.KeyframeResolver=Sn,t.LayoutGroup=ka,t.LayoutGroupContext=m,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,s]=e.useState(!ja(n)),o=e.useRef(void 0);if(!ja(n)){const{renderer:t,...e}=n;o.current=t,Oa(e)}return e.useEffect(()=>{ja(n)&&n().then(({renderer:t,...e})=>{Oa(e),o.current=t,s(!0)})},[]),d(La.Provider,{value:{renderer:o.current,strict:i},children:t})},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&Wa(n),(i={...e.useContext(to),...i}).isStatic=f(()=>i.isStatic);const s=e.useMemo(()=>i,[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return d(to.Provider,{value:s,children:t})},t.MotionConfigContext=to,t.MotionContext=Ja,t.MotionGlobalConfig=S,t.MotionValue=Oi,t.NativeAnimation=In,t.NativeAnimationExtended=Un,t.NativeAnimationWrapper=_n,t.PresenceContext=v,t.Reorder=Wu,t.SubscriptionManager=L,t.SwitchLayoutGroupContext=fl,t.ViewTransitionBuilder=qs,t.VisualElement=wa,t.WillChangeMotionValue=Fc,t.acceleratedValues=bi,t.activeAnimations=mt,t.addAttrValue=Vi,t.addPointerEvent=Yl,t.addPointerInfo=Hl,t.addScaleCorrector=xr,t.addStyleValue=Fi,t.addUniqueItem=x,t.alpha=Tt,t.analyseComplexValue=Xt,t.animate=oc,t.animateMini=ac,t.animateValue=function(t){return new qe(t)},t.animateView=function(t,e={}){return new qs(t,e)},t.animateVisualElement=Dl,t.animationControls=Nc,t.animationMapKey=qn,t.animations=zl,t.anticipate=Y,t.applyGeneratorOptions=Bn,t.applyPxDefaults=Ti,t.attachSpring=Rs,t.attrEffect=Ri,t.backIn=X,t.backInOut=H,t.backOut=z,t.buildTransform=Sa,t.calcGeneratorDuration=me,t.calcLength=uo,t.cancelFrame=lt,t.cancelMicrotask=zi,t.cancelSync=Qs,t.circIn=K,t.circInOut=_,t.circOut=G,t.clamp=P,t.collectMotionValues=Li,t.color=Wt,t.complex=Gt,t.convertOffsetToTimes=$e,t.createBox=ur,t.createGeneratorEasing=fe,t.createRenderBatcher=rt,t.createScopedAnimate=sc,t.cubicBezier=U,t.cubicBezierAsString=Dn,t.defaultEasing=ze,t.defaultOffset=Ne,t.defaultTransformValue=an,t.defaultValueTypes=fi,t.degrees=kt,t.delay=Do,t.dimensionValueTypes=oi,t.disableInstantTransitions=function(){S.instantAnimations=!1},t.distance=Gl,t.distance2D=_l,t.domAnimation=Rc,t.domMax=Dc,t.domMin=kc,t.easeIn=Z,t.easeInOut=J,t.easeOut=q,t.easingDefinitionToFunction=it,t.fillOffset=Ue,t.fillWildcards=Je,t.filterProps=Ua,t.findDimensionValueType=ri,t.findValueType=Os,t.flushKeyframeResolvers=bn,t.frame=at,t.frameData=ut,t.frameSteps=ct,t.generateLinearEasing=de,t.getAnimatableNone=yi,t.getAnimationMap=Jn,t.getComputedStyle=ss,t.getDefaultValueType=gi,t.getEasingForSegment=tt,t.getMixer=re,t.getOriginIndex=As,t.getValueAsType=Ai,t.getValueTransition=ni,t.getVariableValue=ei,t.getViewAnimationLayerInfo=Ns,t.getViewAnimations=zs,t.hasWarned=function(t){return I.has(t)},t.hex=Rt,t.hover=_i,t.hsla=Ft,t.hslaToRgba=Zt,t.inView=Mc,t.inertia=Fe,t.interpolate=We,t.invariant=b,t.invisibleValues=ie,t.isBezierDefinition=et,t.isBrowser=g,t.isCSSVariableName=gt,t.isCSSVariableToken=vt,t.isDragActive=Hi,t.isDragging=Xi,t.isEasingArray=Q,t.isGenerator=jn,t.isHTMLElement=ki,t.isMotionComponent=Yc,t.isMotionValue=Vs,t.isNodeOrChild=Zi,t.isNumericalString=E,t.isObject=A,t.isPrimaryPointer=qi,t.isSVGElement=os,t.isSVGSVGElement=Es,t.isValidMotionProp=Ia,t.isWaapiSupportedEasing=function t(e){return Boolean("function"==typeof e&&Rn()||!e||"string"==typeof e&&(e in kn||Rn())||et(e)||Array.isArray(e)&&e.every(t))},t.isZeroValueString=M,t.keyframes=Xe,t.m=Cc,t.makeAnimationInstant=$n,t.makeUseVisualState=ul,t.mapEasingToNativeEasing=Ln,t.mapValue=function(t,e,n,i){const s=Ms(e,n,i);return Cs(()=>s(t.get()))},t.maxGeneratorDuration=pe,t.memo=C,t.microtask=$i,t.millisecondsToSeconds=j,t.mirrorEasing=N,t.mix=ce,t.mixArray=ae,t.mixColor=ne,t.mixComplex=ue,t.mixImmediate=qt,t.mixLinearColor=Qt,t.mixNumber=Jt,t.mixObject=le,t.mixVisibility=se,t.motion=Mu,t.motionValue=ji,t.moveItem=T,t.noop=V,t.number=wt,t.numberValueTypes=mi,t.observeTimeline=vs,t.optimizedAppearDataAttribute=Mo,t.parseCSSVariable=ti,t.parseValueFromTransform=ln,t.percent=Lt,t.pipe=D,t.positionalKeys=ii,t.press=is,t.progress=k,t.progressPercentage=It,t.propEffect=Di,t.px=Ot,t.readTransformValue=un,t.recordStats=function(){if(ot.value)throw bs(),new Error("Stats are already being measured");const t=ot;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},at.postRender(xs,!0),Ss},t.removeItem=w,t.resize=ys,t.resolveElements=Si,t.resolveMotionValue=ko,t.reverseEasing=$,t.rgbUnit=Ct,t.rgba=Vt,t.scale=Pt,t.scroll=Ec,t.scrollInfo=Pc,t.secondsToMilliseconds=O,t.setDragLock=Yi,t.setStyle=An,t.spring=Ie,t.springValue=function(t,e){const n=ji(Vs(t)?t.get():t);return Rs(n,t,e),n},t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:As(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=it(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.MotionIsMounted)return;const o=t.dataset[Ao];if(!o)return;window.MotionHandoffAnimation=Jc;const r=qc(o,e);th||(th=On(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),_c.set(r,{animation:th,startTime:null}),window.MotionHandoffAnimation=Jc,window.MotionHasOptimisedAnimation=(t,e)=>{if(!t)return!1;if(!e)return Zc.has(t);const n=qc(t,e);return Boolean(_c.get(n))},window.MotionHandoffMarkAsComplete=t=>{Zc.has(t)&&Zc.set(t,!0)},window.MotionHandoffIsComplete=t=>!0===Zc.get(t),window.MotionCancelOptimisedAnimation=(t,e,n,i)=>{const s=qc(t,e),o=_c.get(s);o&&(n&&void 0===i?n.postRender(()=>{n.postRender(()=>{o.animation.cancel()})}):o.animation.cancel(),n&&i?(eh.add(o),n.render(nh)):(_c.delete(s),_c.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(t,e,n)=>{const i=Co(t);if(!i)return;const s=window.MotionHasOptimisedAnimation?.(i,e),o=t.props.values?.[e];if(!s||!o)return;const r=n.on("change",t=>{o.get()!==t&&(window.MotionCancelOptimisedAnimation?.(i,e),r())});return r});const a=()=>{th.cancel();const o=On(t,e,n,i);void 0===Qc&&(Qc=performance.now()),o.startTime=Qc,_c.set(r,{animation:o,startTime:Qc}),s&&s(o)};Zc.set(o,!1),th.ready?th.ready.then(a).catch(V):a()},t.startWaapiAnimation=On,t.statsBuffer=ot,t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,s="end"===e?Math.floor(i):Math.ceil(i);return P(0,1,s/t)}},t.styleEffect=Wi,t.supportedWaapiEasing=kn,t.supportsBrowserAnimation=Hn,t.supportsFlags=Cn,t.supportsLinearEasing=Rn,t.supportsPartialKeyframes=Pi,t.supportsScrollTimeline=Mn,t.svgEffect=Ni,t.sync=Js,t.testValueType=si,t.time=pt,t.transform=Ms,t.transformPropOrder=hn,t.transformProps=dn,t.transformValue=Cs,t.transformValueTypes=pi,t.unwrapMotionComponent=function(t){if(Yc(t))return t[dl]},t.useAnimate=function(){const t=f(()=>({current:null,animations:[]})),e=f(()=>sc(t));return Vc(()=>{t.animations.forEach(t=>t.stop()),t.animations.length=0}),[t,e]},t.useAnimateMini=function(){const t=f(()=>({current:null,animations:[]})),e=f(()=>rc(t));return Vc(()=>{t.animations.forEach(t=>t.stop())}),[t,e]},t.useAnimation=zc,t.useAnimationControls=$c,t.useAnimationFrame=Ic,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]),o=e.useCallback(e=>{n.current="number"!=typeof e?F(0,t.length,n.current+1):e,s(t[n.current])},[t.length,...t]);return[i,o]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=oh({},!1),o=f(()=>new sh({props:{onUpdate:t=>{i({...t})}},visualState:s,presenceContext:null},{initialState:t}));return e.useLayoutEffect(()=>(o.mount({}),()=>o.unmount()),[o]),[n,f(()=>t=>Dl(o,t))]},t.useDeprecatedInvertedScale=function(t){let n=ku(1),i=ku(1);const{visualElement:s}=e.useContext(Ja);return t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:Ou(n,ah),scaleY:Ou(i,ah)}},t.useDomEvent=function(t,n,i,s){e.useEffect(()=>{const e=t.current;if(i&&e)return Zr(e,n,i,s)},[t,n,i,s])},t.useDragControls=function(){return f(Hc)},t.useElementScroll=function(t){return Bc({container:t})},t.useForceUpdate=Ra,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1,initial:r=!1}={}){const[a,l]=e.useState(r);return e.useEffect(()=>{if(!t.current||o&&a)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return Mc(t.current,()=>(l(!0),o?void 0:()=>l(!1)),e)},[n,t,i,o,s]),a},t.useInstantLayoutTransition=Kc,t.useInstantTransition=function(){const[t,n]=Ra(),i=Kc(),s=e.useRef(-1);return e.useEffect(()=>{at.postRender(()=>at.postRender(()=>{n===s.current&&(S.instantAnimations=!1)}))},[n]),e=>{i(()=>{S.instantAnimations=!0,t(),e(),s.current=n+1})}},t.useIsPresent=function(){return null===(t=e.useContext(v))||t.isPresent;var t},t.useIsomorphicLayoutEffect=y,t.useMotionTemplate=function(t,...e){const n=t.length;return Lu(e.filter(Vs),function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=Vs(n)?n.get():n)}return i})},t.useMotionValue=ku,t.useMotionValueEvent=Lc,t.usePageInView=function(){const[t,n]=e.useState(!0);return e.useEffect(()=>{const t=()=>n(!document.hidden);return document.hidden&&t(),document.addEventListener("visibilitychange",t),()=>{document.removeEventListener("visibilitychange",t)}},[]),t},t.usePresence=oo,t.usePresenceData=function(){const t=e.useContext(v);return t?t.custom:void 0},t.useReducedMotion=Wc,t.useReducedMotionConfig=function(){const t=Wc(),{reducedMotion:n}=e.useContext(to);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return e.useCallback(()=>{const t=Jr.current;t&&t.resetTree()},[])},t.useScroll=Bc,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(to),s=()=>Vs(t)?t.get():t;if(i)return Ou(s);const o=ku(s());return e.useInsertionEffect(()=>Rs(o,t,n),[o,JSON.stringify(n)]),o},t.useTime=function(){const t=ku(0);return Ic(e=>t.set(e)),t},t.useTransform=Ou,t.useUnmountEffect=Vc,t.useVelocity=function(t){const e=ku(t.getVelocity()),n=()=>{const i=t.getVelocity();e.set(i),i&&at.update(n)};return Lc(t,"change",()=>{at.update(n,!1,!0)}),e},t.useViewportScroll=function(){return Bc()},t.useWillChange=function(){return f(()=>new Fc("auto"))},t.velocityPerSecond=B,t.vh=jt,t.visualElementStore=ca,t.vw=Bt,t.warnOnce=function(t,e,n){t||I.has(e)||(console.warn(function(t,e){return e?`${t}. For more information and steps for solving, visit https://motion.dev/troubleshooting/${e}`:t}(e,n)),I.add(e))},t.warning=()=>{},t.wrap=F});
