import{n as t,p as e,q as n,t as s,u as i,v as r,w as o,x as a,y as u,z as l,A as h,r as c,b as d,o as p,B as m,a as f,C as y,j as v,D as g,m as b,i as w,h as T,l as S,E as A,d as M,s as V,F as x,k as C,e as k,f as P,g as E}from"./size-rollup-dom-animation-assets.js";import{Fragment as F}from"react";const O={},D=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);const I=t=>/^0[^.\s]+$/u.test(t);function R(t){let e;return()=>(void 0===e&&(e=t()),e)}const N=t=>t,K=(t,e)=>n=>e(t(n)),B=(...t)=>t.reduce(K),L=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class j{constructor(){this.subscriptions=[]}add(t){var e,n;return e=this.subscriptions,n=t,-1===e.indexOf(n)&&e.push(n),()=>function(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let i=0;i<s;i++){const s=this.subscriptions[i];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const U=t=>1e3*t,q=t=>t/1e3;function W(t,e){return e?t*(1e3/e):0}const Y=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function $(t,e,n,s){if(t===e&&n===s)return N;const i=e=>function(t,e,n,s,i){let r,o,a=0;do{o=e+(n-e)/2,r=Y(o,s,i)-t,r>0?n=o:e=o}while(Math.abs(r)>1e-7&&++a<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:Y(i(t),e,s)}const z=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,H=t=>e=>1-t(1-e),X=$(.33,1.53,.69,.99),_=H(X),G=z(_),Z=t=>(t*=2)<1?.5*_(t):.5*(2-Math.pow(2,-10*(t-1))),J=t=>1-Math.sin(Math.acos(t)),Q=H(J),tt=z(J),et=$(.42,0,1,1),nt=$(0,0,.58,1),st=$(.42,0,.58,1),it=t=>Array.isArray(t)&&"number"==typeof t[0],rt={linear:N,easeIn:et,easeInOut:st,easeOut:nt,circIn:J,circInOut:tt,circOut:Q,backIn:_,backInOut:G,backOut:X,anticipate:Z},ot=t=>{if(it(t)){t.length;const[e,n,s,i]=t;return $(e,n,s,i)}return"string"==typeof t?rt[t]:t},at=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ut={value:null,addProjectionMetrics:null};function lt(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,o=at.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,i=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},u=0;function l(e){o.has(e)&&(h.schedule(e),t()),u++,e(a)}const h={schedule:(t,e=!1,r=!1)=>{const a=r&&i?n:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{a=t,i?r=!0:(i=!0,[n,s]=[s,n],n.forEach(l),e&&ut.value&&ut.value.frameloop[e].push(u),u=0,n.clear(),i=!1,r&&(r=!1,h.process(t)))}};return h}(r,e?n:void 0),t),{}),{setup:a,read:u,resolveKeyframes:l,preUpdate:h,update:c,preRender:d,render:p,postRender:m}=o,f=()=>{const r=O.useManualTiming?i.timestamp:performance.now();n=!1,O.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(r-i.timestamp,40),1)),i.timestamp=r,i.isProcessing=!0,a.process(i),u.process(i),l.process(i),h.process(i),c.process(i),d.process(i),p.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(f))};return{schedule:at.reduce((e,r)=>{const a=o[r];return e[r]=(e,r=!1,o=!1)=>(n||(n=!0,s=!0,i.isProcessing||t(f)),a.schedule(e,r,o)),e},{}),cancel:t=>{for(let e=0;e<at.length;e++)o[at[e]].cancel(t)},state:i,steps:o}}const{schedule:ht,cancel:ct,state:dt,steps:pt}=lt("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:N,!0);let mt;function ft(){mt=void 0}const yt={now:()=>(void 0===mt&&yt.set(dt.isProcessing||O.useManualTiming?dt.timestamp:performance.now()),mt),set:t=>{mt=t,queueMicrotask(ft)}},vt=t=>Math.round(1e5*t)/1e5,gt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const bt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,wt=(t,e)=>n=>Boolean("string"==typeof n&&bt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Tt=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,o,a]=s.match(gt);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},St={...t,transform:t=>Math.round((t=>n(0,255,t))(t))},At={test:wt("rgb","red"),parse:Tt("red","green","blue"),transform:({red:t,green:n,blue:s,alpha:i=1})=>"rgba("+St.transform(t)+", "+St.transform(n)+", "+St.transform(s)+", "+vt(e.transform(i))+")"};const Mt={test:wt("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:At.transform},Vt={test:wt("hsl","hue"),parse:Tt("hue","saturation","lightness"),transform:({hue:t,saturation:n,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+s.transform(vt(n))+", "+s.transform(vt(i))+", "+vt(e.transform(r))+")"},xt={test:t=>At.test(t)||Mt.test(t)||Vt.test(t),parse:t=>At.test(t)?At.parse(t):Vt.test(t)?Vt.parse(t):Mt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?At.transform(t):Vt.transform(t),getAnimatableNone:t=>{const e=xt.parse(t);return e.alpha=0,xt.transform(e)}},Ct=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const kt="number",Pt="color",Et=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ft(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(Et,t=>(xt.test(t)?(s.color.push(r),i.push(Pt),n.push(xt.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push(kt),n.push(parseFloat(t))),++r,"${}")).split("${}");return{values:n,split:o,indexes:s,types:i}}function Ot(t){return Ft(t).values}function Dt(t){const{split:e,types:n}=Ft(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===kt?vt(t[r]):e===Pt?xt.transform(t[r]):t[r]}return i}}const It=t=>"number"==typeof t?0:xt.test(t)?xt.getAnimatableNone(t):t;const Rt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(gt)?.length||0)+(t.match(Ct)?.length||0)>0},parse:Ot,createTransformer:Dt,getAnimatableNone:function(t){const e=Ot(t);return Dt(t)(e.map(It))}};function Nt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Kt(t,e){return n=>n>0?e:t}const Bt=(t,e,n)=>t+(e-t)*n,Lt=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},jt=[Mt,At,Vt];function Ut(t){const e=(n=t,jt.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===Vt&&(s=function({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,o=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,a=2*n-s;i=Nt(a,s,t+1/3),r=Nt(a,s,t),o=Nt(a,s,t-1/3)}else i=r=o=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(s)),s}const qt=(t,e)=>{const n=Ut(t),s=Ut(e);if(!n||!s)return Kt(t,e);const i={...n};return t=>(i.red=Lt(n.red,s.red,t),i.green=Lt(n.green,s.green,t),i.blue=Lt(n.blue,s.blue,t),i.alpha=Bt(n.alpha,s.alpha,t),At.transform(i))},Wt=new Set(["none","hidden"]);function Yt(t,e){return n=>Bt(t,e,n)}function $t(t){return"number"==typeof t?Yt:"string"==typeof t?i(t)?Kt:xt.test(t)?qt:Xt:Array.isArray(t)?zt:"object"==typeof t?xt.test(t)?qt:Ht:Kt}function zt(t,e){const n=[...t],s=n.length,i=t.map((t,n)=>$t(t)(t,e[n]));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function Ht(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=$t(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const Xt=(t,e)=>{const n=Rt.createTransformer(e),s=Ft(t),i=Ft(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Wt.has(t)&&!i.values.length||Wt.has(e)&&!s.values.length?function(t,e){return Wt.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):B(zt(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}(s,i),i.values),n):Kt(t,e)};function _t(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Bt(t,e,n);return $t(t)(t,e)}const Gt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>ht.update(e,t),stop:()=>ct(e),now:()=>dt.isProcessing?dt.timestamp:yt.now()}},Zt=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},Jt=2e4;function Qt(t){let e=0;let n=t.next(e);for(;!n.done&&e<Jt;)e+=50,n=t.next(e);return e>=Jt?1/0:e}function te(t,e,n){const s=Math.max(e-5,0);return W(n-t(s),e-s)}const ee=100,ne=10,se=1,ie=0,re=800,oe=.3,ae=.3,ue={granular:.01,default:2},le={granular:.005,default:.5},he=.01,ce=10,de=.05,pe=1,me=.001;function fe({duration:t=re,bounce:e=oe,velocity:s=ie,mass:i=se}){let r,o,a=1-e;a=n(de,pe,a),t=n(he,ce,q(t)),a<1?(r=e=>{const n=e*a,i=n*t,r=n-s,o=ve(e,a),u=Math.exp(-i);return me-r/o*u},o=e=>{const n=e*a*t,i=n*s+s,o=Math.pow(a,2)*Math.pow(e,2)*t,u=Math.exp(-n),l=ve(Math.pow(e,2),a);return(-r(e)+me>0?-1:1)*((i-o)*u)/l}):(r=e=>Math.exp(-e*t)*((e-s)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(s-e)));const u=function(t,e,n){let s=n;for(let n=1;n<ye;n++)s-=t(s)/e(s);return s}(r,o,5/t);if(t=U(t),isNaN(u))return{stiffness:ee,damping:ne,duration:t};{const e=Math.pow(u,2)*i;return{stiffness:e,damping:2*a*Math.sqrt(i*e),duration:t}}}const ye=12;function ve(t,e){return t*Math.sqrt(1-e*e)}const ge=["duration","bounce"],be=["stiffness","damping","mass"];function we(t,e){return e.some(e=>void 0!==t[e])}function Te(t=ae,e=oe){const s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:r}=s;const o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],u={done:!1,value:o},{stiffness:l,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:ie,stiffness:ee,damping:ne,mass:se,isResolvedFromDuration:!1,...t};if(!we(t,be)&&we(t,ge))if(t.visualDuration){const s=t.visualDuration,i=2*Math.PI/(1.2*s),r=i*i,o=2*n(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:se,stiffness:r,damping:o}}else{const n=fe(t);e={...e,...n,mass:se},e.isResolvedFromDuration=!0}return e}({...s,velocity:-q(s.velocity||0)}),f=p||0,y=h/(2*Math.sqrt(l*c)),v=a-o,g=q(Math.sqrt(l/c)),b=Math.abs(v)<5;let w;if(i||(i=b?ue.granular:ue.default),r||(r=b?le.granular:le.default),y<1){const t=ve(g,y);w=e=>{const n=Math.exp(-y*g*e);return a-n*((f+y*g*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}}else if(1===y)w=t=>a-Math.exp(-g*t)*(v+(f+g*v)*t);else{const t=g*Math.sqrt(y*y-1);w=e=>{const n=Math.exp(-y*g*e),s=Math.min(t*e,300);return a-n*((f+y*g*v)*Math.sinh(s)+t*v*Math.cosh(s))/t}}const T={calculatedDuration:m&&d||null,next:t=>{const e=w(t);if(m)u.done=t>=d;else{let n=0===t?f:0;y<1&&(n=0===t?U(f):te(w,t,e));const s=Math.abs(n)<=i,o=Math.abs(a-e)<=r;u.done=s&&o}return u.value=u.done?a:e,u},toString:()=>{const t=Math.min(Qt(T),Jt),e=Zt(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function Se({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:u,restDelta:l=.5,restSpeed:h}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?u:void 0===u||Math.abs(a-t)<Math.abs(u-t)?a:u;let m=n*e;const f=c+m,y=void 0===o?f:o(f);y!==f&&(m=y-c);const v=t=>-m*Math.exp(-t/s),g=t=>y+v(t),b=t=>{const e=v(t),n=g(t);d.done=Math.abs(e)<=l,d.value=d.done?y:n};let w,T;const S=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==u&&e>u)&&(w=t,T=Te({keyframes:[d.value,p(d.value)],velocity:te(g,t,d.value),damping:i,stiffness:r,restDelta:l,restSpeed:h}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,b(t),S(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&b(t),d)}}}function Ae(t,e,{clamp:s=!0,ease:i,mixer:r}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const a=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const u=function(t,e,n){const s=[],i=n||O.mix||_t,r=t.length-1;for(let n=0;n<r;n++){let r=i(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||N:e;r=B(t,r)}s.push(r)}return s}(e,i,r),l=u.length,h=n=>{if(a&&n<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(n<t[s+1]);s++);const i=L(t[s],t[s+1],n);return u[s](i)};return s?e=>h(n(t[0],t[o-1],e)):h}function Me(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=L(0,e,s);t.push(Bt(n,1,i))}}(e,t.length-1),e}function Ve({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=(t=>Array.isArray(t)&&"number"!=typeof t[0])(s)?s.map(ot):ot(s),r={done:!1,value:e[0]},o=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Me(e),t),a=Ae(o,e,{ease:Array.isArray(i)?i:(u=e,l=i,u.map(()=>l||st).splice(0,u.length-1))});var u,l;return{calculatedDuration:t,next:e=>(r.value=a(e),r.done=e>=t,r)}}Te.applyToOptions=t=>{const e=function(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Qt(s),Jt);return{type:"keyframes",ease:t=>s.next(i*t).value/e,duration:q(i)}}(t,100,Te);return t.ease=e.ease,t.duration=U(e.duration),t.type="keyframes",t};const xe=t=>null!==t;function Ce(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(xe),o=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return o&&void 0!==s?s:r[o]}const ke={decay:Se,inertia:Se,tween:Ve,keyframes:Ve,spring:Te};function Pe(t){"string"==typeof t.type&&(t.type=ke[t.type])}class Ee{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Fe=t=>t/100;class Oe extends Ee{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==yt.now()&&this.tick(yt.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Pe(t);const{type:e=Ve,repeat:n=0,repeatDelay:s=0,repeatType:i,velocity:r=0}=t;let{keyframes:o}=t;const a=e||Ve;a!==Ve&&"number"!=typeof o[0]&&(this.mixKeyframes=B(Fe,_t(o[0],o[1])),o=[0,100]);const u=a({...t,keyframes:o});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===u.calculatedDuration&&(u.calculatedDuration=Qt(u));const{calculatedDuration:l}=u;this.calculatedDuration=l,this.resolvedDuration=l+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=u}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:u}=this;if(null===this.startTime)return s.next(0);const{delay:l=0,keyframes:h,repeat:c,repeatType:d,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const v=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?v<0:v>i;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let b=this.currentTime,w=s;if(c){const t=Math.min(this.currentTime,i)/a;let e=Math.floor(t),s=t%1;!s&&t>=1&&(s=1),1===s&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===d?(s=1-s,p&&(s-=p/a)):"mirror"===d&&(w=o)),b=n(0,1,s)*a}const T=g?{done:!1,value:h[0]}:w.next(b);r&&(T.value=r(T.value));let{done:S}=T;g||null===u||(S=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return A&&m!==Se&&(T.value=Ce(h,this.options,y,this.speed)),f&&f(T.value),A&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return q(this.calculatedDuration)}get time(){return q(this.currentTime)}set time(t){t=U(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(yt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=q(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Gt,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(yt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const De=t=>180*t/Math.PI,Ie=t=>{const e=De(Math.atan2(t[1],t[0]));return Ne(e)},Re={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ie,rotateZ:Ie,skewX:t=>De(Math.atan(t[1])),skewY:t=>De(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ne=t=>((t%=360)<0&&(t+=360),t),Ke=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Be=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Le={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ke,scaleY:Be,scale:t=>(Ke(t)+Be(t))/2,rotateX:t=>Ne(De(Math.atan2(t[6],t[5]))),rotateY:t=>Ne(De(Math.atan2(-t[2],t[0]))),rotateZ:Ie,rotate:Ie,skewX:t=>De(Math.atan(t[4])),skewY:t=>De(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function je(t){return t.includes("scale")?1:0}function Ue(t,e){if(!t||"none"===t)return je(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Le,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Re,i=e}if(!i)return je(e);const r=s[e],o=i[1].split(",").map(qe);return"function"==typeof r?r(o):o[r]}function qe(t){return parseFloat(t.trim())}const We=e=>e===t||e===r,Ye=new Set(["x","y","z"]),$e=o.filter(t=>!Ye.has(t));const ze={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Ue(e,"x"),y:(t,{transform:e})=>Ue(e,"y")};ze.translateX=ze.x,ze.translateY=ze.y;const He=new Set;let Xe=!1,_e=!1,Ge=!1;function Ze(){if(_e){const t=Array.from(He).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return $e.forEach(n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}_e=!1,Xe=!1,He.forEach(t=>t.complete(Ge)),He.clear()}function Je(){He.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(_e=!0)})}class Qe{constructor(t,e,n,s,i,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(He.add(this),Xe||(Xe=!0,ht.read(Je),ht.resolveKeyframes(Ze))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),He.delete(this)}cancel(){"scheduled"===this.state&&(He.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const tn=R(()=>void 0!==window.ScrollTimeline),en={};function nn(t,e){const n=R(t);return()=>en[e]??n()}const sn=nn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),rn=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,on={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:rn([0,.65,.55,1]),circOut:rn([.55,0,1,.45]),backIn:rn([.31,.01,.66,-.59]),backOut:rn([.33,1.53,.69,.99])};function an(t,e){return t?"function"==typeof t?sn()?Zt(t,e):"ease-out":it(t)?rn(t):Array.isArray(t)?t.map(t=>an(t,e)||on.easeOut):on[t]:void 0}function un(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:u}={},l=void 0){const h={[e]:n};u&&(h.offset=u);const c=an(a,i);Array.isArray(c)&&(h.easing=c);const d={delay:s,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};l&&(d.pseudoElement=l);return t.animate(h,d)}function ln(t){return"function"==typeof t&&"applyToOptions"in t}class hn extends Ee{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:i,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(i),this.allowFlatten=r,this.options=t,t.type;const u=function({type:t,...e}){return ln(t)&&sn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=un(e,n,s,u,i),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const t=Ce(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return q(Number(t))}get time(){return q(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=U(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&tn()?(this.animation.timeline=t,N):e(this)}}const cn={anticipate:Z,backInOut:G,circInOut:tt};function dn(t){"string"==typeof t.ease&&t.ease in cn&&(t.ease=cn[t.ease])}class pn extends hn{constructor(t){dn(t),Pe(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:i,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new Oe({...r,autoplay:!1}),a=U(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const mn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Rt.test(t)&&"0"!==t||t.startsWith("url(")));function fn(t){t.duration=0,t.type}const yn=new Set(["opacity","clipPath","filter","transform"]),vn=R(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class gn extends Ee{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:o,name:a,motionValue:u,element:l,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=yt.now();const c={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:a,motionValue:u,element:l,...h},d=l?.KeyframeResolver||Qe;this.keyframeResolver=new d(o,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),a,u,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,s){this.keyframeResolver=void 0;const{name:i,type:r,velocity:o,delay:a,isHandoff:u,onUpdate:l}=n;this.resolvedAt=yt.now(),function(t,e,n,s){const i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],o=mn(i,e),a=mn(r,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||ln(n))&&s)}(t,i,r,o)||(!O.instantAnimations&&a||l?.(Ce(t,n,e)),t[0]=t[t.length-1],fn(n),n.repeat=0);const h={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},c=!u&&function(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:o}=t,a=e?.owner?.current;if(!(a instanceof HTMLElement))return!1;const{onUpdate:u,transformTemplate:l}=e.owner.getProps();return vn()&&n&&yn.has(n)&&("transform"!==n||!l)&&!u&&!s&&"mirror"!==i&&0!==r&&"inertia"!==o}(h)?new pn({...h,element:h.motionValue.owner.current}):new Oe(h);c.finished.then(()=>this.notifyFinished()).catch(N),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Ge=!0,Je(),Ze(),Ge=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const bn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function wn(t,e,n=1){const[s,r]=function(t){const e=bn.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const t=o.trim();return D(t)?parseFloat(t):t}return i(r)?wn(r,e,n+1):r}function Tn(t,e){return t?.[e]??t?.default??t}const Sn=new Set(["width","height","top","left","right","bottom",...o]),An=t=>e=>e.test(t),Mn=[t,r,s,a,u,l,{test:t=>"auto"===t,parse:t=>t}],Vn=t=>Mn.find(An(t));function xn(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||I(t))}const Cn=new Set(["brightness","contrast","saturate","opacity"]);function kn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(gt)||[];if(!s)return t;const i=n.replace(s,"");let r=Cn.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Pn=/\b([a-z-]*)\(.*?\)/gu,En={...Rt,getAnimatableNone:t=>{const e=t.match(Pn);return e?e.map(kn).join(" "):t}},Fn={...h,color:xt,backgroundColor:xt,outlineColor:xt,fill:xt,stroke:xt,borderColor:xt,borderTopColor:xt,borderRightColor:xt,borderBottomColor:xt,borderLeftColor:xt,filter:En,WebkitFilter:En},On=t=>Fn[t];function Dn(t,e){let n=On(t);return n!==En&&(n=Rt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const In=new Set(["auto","none","0"]);class Rn extends Qe{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),i(s))){const i=wn(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!Sn.has(n)||2!==t.length)return;const[s,r]=t,o=Vn(s),a=Vn(r);if(o!==a)if(We(o)&&We(a))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else ze[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||xn(t[e]))&&n.push(e);n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!In.has(e)&&Ft(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=Dn(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ze[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=ze[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}class Nn{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{const e=yt.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=yt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new j);const n=this.events[t].add(e);return"change"===t?()=>{n(),ht.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=yt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return W(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Kn(t,e){return new Nn(t,e)}const{schedule:Bn,cancel:Ln}=lt(queueMicrotask,!1),jn=!1;function Un(){return jn}function qn(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function Wn(t){return!("touch"===t.pointerType||Un())}const Yn=(t,e)=>!!e&&(t===e||Yn(t,e.parentElement)),$n=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const zn=new WeakSet;function Hn(t){return e=>{"Enter"===e.key&&t(e)}}function Xn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function _n(t){return(t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary)(t)&&!Un()}function Gn(t,e,n={}){const[s,i,r]=qn(t,n),o=t=>{const s=t.currentTarget;if(!_n(t))return;zn.add(s);const r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",u),zn.has(s)&&zn.delete(s),_n(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,s===window||s===document||n.useGlobalTarget||Yn(s,t.target))},u=t=>{o(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",u,i)};return s.forEach(t=>{var e,s;(n.useGlobalTarget?window:t).addEventListener("pointerdown",o,i),"object"==typeof(s=e=t)&&null!==s&&"offsetHeight"in e&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=Hn(()=>{if(zn.has(n))return;Xn(n,"down");const t=Hn(()=>{Xn(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>Xn(n,"cancel"),e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)})(t,i)),function(t){return $n.has(t.tagName)||-1!==t.tabIndex}(t)||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}const Zn=[...Mn,xt,Rt];function Jn(t,e,n){const s=t.getProps();return c(s,e,void 0!==n?n:s.custom,t)}const Qn=t=>Array.isArray(t);function ts(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Kn(n))}function es(t){return Qn(t)?t[t.length-1]||0:t}function ns(t,e){const n=t.getValue("willChange");if(s=n,Boolean(d(s)&&s.add))return n.add(e);if(!n&&O.WillChange){const n=new O.WillChange("auto");t.addValue("willChange",n),n.add(e)}var s}function ss(t){return t.props[p]}const is=t=>null!==t;const rs={type:"spring",stiffness:500,damping:25,restSpeed:10},os={type:"keyframes",duration:.8},as={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},us=(t,{keyframes:e})=>e.length>2?os:m.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:rs:as;const ls=(t,e,n,s={},i,r)=>o=>{const a=Tn(s,t)||{},u=a.delay||s.delay||0;let{elapsed:l=0}=s;l-=U(u);const h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-l,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:i};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:u,elapsed:l,...h}){return!!Object.keys(h).length})(a)||Object.assign(h,us(t,h)),h.duration&&(h.duration=U(h.duration)),h.repeatDelay&&(h.repeatDelay=U(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(fn(h),0===h.delay&&(c=!0)),(O.instantAnimations||O.skipAnimations)&&(c=!0,fn(h),h.delay=0),h.allowFlatten=!a.type&&!a.ease,c&&!r&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(is),r=e&&"loop"!==n&&e%2==1?0:i.length-1;return r&&void 0!==s?s:i[r]}(h.keyframes,a);if(void 0!==t)return void ht.update(()=>{h.onUpdate(t),h.onComplete()})}return a.isSync?new Oe(h):new gn(h)};function hs({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function cs(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const u=[],l=i&&t.animationState&&t.animationState.getState()[i];for(const e in a){const s=t.getValue(e,t.latestValues[e]??null),i=a[e];if(void 0===i||l&&hs(l,e))continue;const o={delay:n,...Tn(r||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(i)&&i===h&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const n=ss(t);if(n){const t=window.MotionHandoffAnimation(n,e,ht);null!==t&&(o.startTime=t,c=!0)}}ns(t,e),s.start(ls(e,s,i,t.shouldReduceMotion&&Sn.has(e)?{type:!1}:o,t,c));const d=s.animation;d&&u.push(d)}return o&&Promise.all(u).then(()=>{ht.update(()=>{o&&function(t,e){const n=Jn(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const e in r)ts(t,e,es(r[e]))}(t,o)})}),u}function ds(t,e,n,s=0,i=1){const r=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),o=t.size,a=(o-1)*s;return"function"==typeof n?n(r,o):1===i?r*s:a-r*s}function ps(t,e,n={}){const s=Jn(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(cs(t,s,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{const{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=i;return function(t,e,n=0,s=0,i=0,r=1,o){const a=[];for(const u of t.variantChildren)u.notify("AnimationStart",e),a.push(ps(u,e,{...o,delay:n+("function"==typeof s?0:s)+ds(t.variantChildren,u,s,i,r)}).then(()=>u.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,s,r,o,a,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[t,e]="beforeChildren"===a?[r,o]:[o,r];return t().then(()=>e())}return Promise.all([r(),o(n.delay)])}function ms(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const fs=y.length;function ys(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&ys(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<fs;n++){const s=y[n],i=t.props[s];(f(i)||!1===i)&&(e[s]=i)}return e}const vs=[...g].reverse(),gs=g.length;function bs(t){return e=>Promise.all(e.map(({animation:e,options:n})=>function(t,e,n={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e)){const i=e.map(e=>ps(t,e,n));s=Promise.all(i)}else if("string"==typeof e)s=ps(t,e,n);else{const i="function"==typeof e?Jn(t,e,n.custom):e;s=Promise.all(cs(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}(t,e,n)))}function ws(t){let e=bs(t),n=As(),s=!0;const i=e=>(n,s)=>{const i=Jn(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(i){const{transition:t,transitionEnd:e,...s}=i;n={...n,...s,...e}}return n};function r(r){const{props:o}=t,a=ys(t.parent)||{},u=[],l=new Set;let h={},c=1/0;for(let e=0;e<gs;e++){const d=vs[e],p=n[d],m=void 0!==o[d]?o[d]:a[d],y=f(m),g=d===r?p.isActive:null;!1===g&&(c=e);let b=m===a[d]&&m!==o[d]&&y;if(b&&s&&t.manuallyAnimateOnMount&&(b=!1),p.protectedKeys={...h},!p.isActive&&null===g||!m&&!p.prevProp||v(m)||"boolean"==typeof m)continue;const w=Ts(p.prevProp,m);let T=w||d===r&&p.isActive&&!b&&y||e>c&&y,S=!1;const A=Array.isArray(m)?m:[m];let M=A.reduce(i(d),{});!1===g&&(M={});const{prevResolvedValues:V={}}=p,x={...V,...M},C=e=>{T=!0,l.has(e)&&(S=!0,l.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in x){const e=M[t],n=V[t];if(h.hasOwnProperty(t))continue;let s=!1;s=Qn(e)&&Qn(n)?!ms(e,n):e!==n,s?null!=e?C(t):l.add(t):void 0!==e&&l.has(t)?C(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=M,p.isActive&&(h={...h,...M}),s&&t.blockInitialAnimation&&(T=!1);const k=b&&w;T&&(!k||S)&&u.push(...A.map(e=>{const n={type:d};if("string"==typeof e&&s&&!k&&t.manuallyAnimateOnMount&&t.parent){const{parent:s}=t,i=Jn(s,e);if(s.enteringChildren&&i){const{delayChildren:e}=i.transition||{};n.delay=ds(s.enteringChildren,t,e)}}return{animation:e,options:n}}))}if(l.size){const e={};if("boolean"!=typeof o.initial){const n=Jn(t,Array.isArray(o.initial)?o.initial[0]:o.initial);n&&n.transition&&(e.transition=n.transition)}l.forEach(n=>{const s=t.getBaseTarget(n),i=t.getValue(n);i&&(i.liveStyle=!0),e[n]=s??null}),u.push({animation:e})}let d=Boolean(u.length);return!s||!1!==o.initial&&o.initial!==o.animate||t.manuallyAnimateOnMount||(d=!1),s=!1,d?e(u):Promise.resolve()}return{animateChanges:r,setActive:function(e,s){if(n[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),n[e].isActive=s;const i=r(e);for(const t in n)n[t].protectedKeys={};return i},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=As(),s=!0}}}function Ts(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!ms(e,t)}function Ss(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function As(){return{animate:Ss(!0),whileInView:Ss(),whileHover:Ss(),whileTap:Ss(),whileDrag:Ss(),whileFocus:Ss(),exit:Ss()}}class Ms{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Vs=0;const xs={animation:{Feature:class extends Ms{constructor(t){super(t),t.animationState||(t.animationState=ws(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();v(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Ms{constructor(){super(...arguments),this.id=Vs++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Cs(t){return{point:{x:t.pageX,y:t.pageY}}}function ks(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===n);const i=s["onHover"+n];i&&ht.postRender(()=>i(e,Cs(e)))}function Ps(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Es(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===n);const i=s["onTap"+("End"===n?"":n)];i&&ht.postRender(()=>i(e,Cs(e)))}const Fs=new WeakMap,Os=new WeakMap,Ds=t=>{const e=Fs.get(t.target);e&&e(t)},Is=t=>{t.forEach(Ds)};function Rs(t,e,n){const s=function({root:t,...e}){const n=t||document;Os.has(n)||Os.set(n,{});const s=Os.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Is,{root:t,...e})),s[i]}(e);return Fs.set(t,n),s.observe(t),()=>{Fs.delete(t),s.unobserve(t)}}const Ns={some:0,all:1};const Ks={inView:{Feature:class extends Ms{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:s="some",once:i}=t,r={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof s?s:Ns[s]};return Rs(this.node.current,r,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,i&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:s}=this.node.getProps(),r=e?n:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Ms{mount(){const{current:t}=this.node;t&&(this.unmount=Gn(t,(t,e)=>(Es(this.node,e,"Start"),(t,{success:e})=>Es(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Ms{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(Ps(this.node.current,"focus",()=>this.onFocus()),Ps(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Ms{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[s,i,r]=qn(t,n),o=t=>{if(!Wn(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const r=t=>{Wn(t)&&(s(t),n.removeEventListener("pointerleave",r))};n.addEventListener("pointerleave",r,i)};return s.forEach(t=>{t.addEventListener("pointerenter",o,i)}),r}(t,(t,e)=>(ks(this.node,e,"Start"),t=>ks(this.node,t,"End"))))}unmount(){}}}};const Bs=()=>({x:{min:0,max:0},y:{min:0,max:0}}),Ls={current:null},js={current:!1};const Us=new WeakMap;const qs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ws{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:i,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Qe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=yt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,ht.render(this.render,!1,!0))};const{latestValues:a,renderState:u}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=w(e),this.isVariantNode=T(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:l,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in h){const e=h[t];void 0!==a[t]&&d(e)&&e.set(a[t])}}mount(t){this.current=t,Us.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),js.current||function(){if(js.current=!0,b)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ls.current=t.matches;t.addEventListener("change",e),e()}else Ls.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ls.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ct(this.notifyUpdate),ct(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=m.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&ht.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in S){const e=S[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<qs.length;e++){const n=qs[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const i=e[s],r=n[s];if(d(i))t.addValue(s,i);else if(d(r))t.addValue(s,Kn(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{const e=t.getStaticValue(s);t.addValue(s,Kn(void 0!==e?e:i,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Kn(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=n&&("string"==typeof n&&(D(n)||I(n))?n=parseFloat(n):(s=n,!Zn.find(An(s))&&Rt.test(e)&&(n=Dn(t,e))),this.setBaseTarget(t,d(n)?n.get():n)),d(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=c(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||d(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new j),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){Bn.render(this.render)}}class Ys extends Ws{constructor(){super(...arguments),this.KeyframeResolver=Rn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;d(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function $s(t,{style:e,vars:n},s,i){const r=t.style;let o;for(o in e)r[o]=e[o];for(o in i?.applyProjectionStyles(r,s),n)r.setProperty(o,n[o])}class zs extends Ys{constructor(){super(...arguments),this.type="html",this.renderInstance=$s}readValueFromInstance(t,e){if(m.has(e))return this.projection?.isProjecting?je(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Ue(n,e)})(t,e);{const s=(n=t,window.getComputedStyle(n)),i=(A(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){M(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return V(t,e,n)}}const Hs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class Xs extends Ys{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Bs}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(m.has(e)){const t=On(e);return t&&t.default||0}return e=Hs.has(e)?e:x(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return C(t,e,n)}build(t,e,n){k(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){!function(t,e,n,s){$s(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(Hs.has(n)?n:x(n),e.attrs[n])}(t,e,0,s)}mount(t){this.isSVGTag=P(t.tagName),super.mount(t)}}const _s={renderer:(t,e)=>E(t)?new Xs(e):new zs(e,{allowProjection:t!==F}),...xs,...Ks};export{_s as domAnimation};
