const t=(t,r,a)=>a>r?r:a<t?t:a;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function a(t){return"string"==typeof t||Array.isArray(t)}const e=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],o=["initial",...e];function s(t){return r(t.animate)||o.some(r=>a(t[r]))}function n(t){return Boolean(s(t)||t.variants)}const i=t=>r=>"string"==typeof r&&r.startsWith(t),f=i("--"),l=i("var(--"),c=t=>!!l(t)&&d.test(t.split("/*")[0].trim()),d=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,p={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},m={...p,transform:r=>t(0,1,r)},u={...p,default:1},g=t=>({test:r=>"string"==typeof r&&r.endsWith(t)&&1===r.split(" ").length,parse:parseFloat,transform:r=>`${r}${t}`}),h=g("deg"),y=g("%"),v=g("px"),w=g("vh"),x=g("vw"),b=(()=>({...y,parse:t=>y.parse(t)/100,transform:t=>y.transform(100*t)}))(),k=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],B=(()=>new Set(k))(),X={...p,transform:Math.round},Y={borderWidth:v,borderTopWidth:v,borderRightWidth:v,borderBottomWidth:v,borderLeftWidth:v,borderRadius:v,radius:v,borderTopLeftRadius:v,borderTopRightRadius:v,borderBottomRightRadius:v,borderBottomLeftRadius:v,width:v,maxWidth:v,height:v,maxHeight:v,top:v,right:v,bottom:v,left:v,padding:v,paddingTop:v,paddingRight:v,paddingBottom:v,paddingLeft:v,margin:v,marginTop:v,marginRight:v,marginBottom:v,marginLeft:v,backgroundPositionX:v,backgroundPositionY:v,...{rotate:h,rotateX:h,rotateY:h,rotateZ:h,scale:u,scaleX:u,scaleY:u,scaleZ:u,skew:h,skewX:h,skewY:h,distance:v,translateX:v,translateY:v,translateZ:v,x:v,y:v,z:v,perspective:v,transformPerspective:v,opacity:m,originX:b,originY:b,originZ:v},zIndex:X,fillOpacity:m,strokeOpacity:m,numOctaves:X},O=(t,r)=>r&&"number"==typeof t?r.transform(t):t,$=t=>Boolean(t&&t.getVelocity),T={};function W(t,{layout:r,layoutId:a}){return B.has(t)||t.startsWith("origin")||(r||void 0!==a)&&(!!T[t]||"opacity"===t)}const L={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},R=k.length;function Z(t,r,a){const{style:e,vars:o,transformOrigin:s}=t;let n=!1,i=!1;for(const t in r){const a=r[t];if(B.has(t))n=!0;else if(f(t))o[t]=a;else{const r=O(a,Y[t]);t.startsWith("origin")?(i=!0,s[t]=r):e[t]=r}}if(r.transform||(n||a?e.transform=function(t,r,a){let e="",o=!0;for(let s=0;s<R;s++){const n=k[s],i=t[n];if(void 0===i)continue;let f=!0;if(f="number"==typeof i?i===(n.startsWith("scale")?1:0):0===parseFloat(i),!f||a){const t=O(i,Y[n]);f||(o=!1,e+=`${L[n]||n}(${t}) `),a&&(r[n]=t)}}return e=e.trim(),a?e=a(r,o?"":e):o&&(e="none"),e}(r,t.transform,a):e.transform&&(e.transform="none")),i){const{originX:t="50%",originY:r="50%",originZ:a=0}=s;e.transformOrigin=`${t} ${r} ${a}`}}const P={offset:"stroke-dashoffset",array:"stroke-dasharray"},S={offset:"strokeDashoffset",array:"strokeDasharray"};function V(t,{attrX:r,attrY:a,attrScale:e,pathLength:o,pathSpacing:s=1,pathOffset:n=0,...i},f,l,c){if(Z(t,i,l),f)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p}=t;d.transform&&(p.transform=d.transform,delete d.transform),(p.transform||d.transformOrigin)&&(p.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),p.transform&&(p.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==r&&(d.x=r),void 0!==a&&(d.y=a),void 0!==e&&(d.scale=e),void 0!==o&&function(t,r,a=1,e=0,o=!0){t.pathLength=1;const s=o?P:S;t[s.offset]=v.transform(-e);const n=v.transform(r),i=v.transform(a);t[s.array]=`${n} ${i}`}(d,o,s,n,!1)}const A=t=>"string"==typeof t&&"svg"===t.toLowerCase(),F=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function I(t){return"string"==typeof t&&!t.includes("-")&&!!(F.indexOf(t)>-1||/[A-Z]/u.test(t))}function z(t){const r=[{},{}];return t?.values.forEach((t,a)=>{r[0][a]=t.get(),r[1][a]=t.getVelocity()}),r}function C(t,r,a,e){if("function"==typeof r){const[o,s]=z(e);r=r(void 0!==a?a:t.custom,o,s)}if("string"==typeof r&&(r=t.variants&&t.variants[r]),"function"==typeof r){const[o,s]=z(e);r=r(void 0!==a?a:t.custom,o,s)}return r}function E(t,r,a){const{style:e}=t,o={};for(const s in e)($(e[s])||r.style&&$(r.style[s])||W(s,t)||void 0!==a?.getValue(s)?.liveStyle)&&(o[s]=e[s]);return o}function H(t,r,a){const e=E(t,r,a);for(const a in t)if($(t[a])||$(r[a])){e[-1!==k.indexOf(a)?"attr"+a.charAt(0).toUpperCase()+a.substring(1):a]=t[a]}return e}const D="undefined"!=typeof window,j={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},q={};for(const t in j)q[t]={isEnabled:r=>j[t].some(t=>!!r[t])};const M=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),U="data-"+M("framerAppearId");export{Y as A,B,o as C,e as D,f as E,M as F,a,$ as b,W as c,Z as d,V as e,A as f,I as g,n as h,s as i,r as j,H as k,q as l,D as m,p as n,U as o,m as p,t as q,C as r,E as s,y as t,c as u,v,k as w,h as x,x as y,w as z};
