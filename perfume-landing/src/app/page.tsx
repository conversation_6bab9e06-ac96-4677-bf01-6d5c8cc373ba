import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation */}
      <nav className="flex items-center justify-between p-6 lg:px-8">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
          <span className="text-xl font-bold text-gray-900">ScentTech</span>
        </div>
        <div className="hidden md:flex items-center space-x-8">
          <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Collection</a>
          <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">About</a>
          <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Contact</a>
        </div>
        <Button variant="outline" className="hidden md:block">
          Shop Now
        </Button>
      </nav>

      {/* Hero Section */}
      <main className="container mx-auto px-6 py-12 lg:py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge variant="secondary" className="bg-rose-100 text-rose-800 hover:bg-rose-200">
                New Collection 2024
              </Badge>
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                The Future of
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-600">
                  {" "}Fragrance{" "}
                </span>
                Experience
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed">
                Introducing our revolutionary smart perfume dispenser.
                Customize your scent, control the intensity, and discover new fragrances with cutting-edge technology.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white">
                Try the Dispenser
              </Button>
              <Button variant="outline" size="lg">
                See How It Works
              </Button>
            </div>

            <div className="flex items-center space-x-8 pt-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">100+</div>
                <div className="text-sm text-gray-600">Fragrance Blends</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">Smart</div>
                <div className="text-sm text-gray-600">AI Technology</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">24/7</div>
                <div className="text-sm text-gray-600">Availability</div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative z-10">
              <div className="w-full h-96 lg:h-[500px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl flex items-center justify-center p-8">
                {/* Smart Perfume Dispenser Illustration */}
                <div className="relative w-80 h-96 bg-white rounded-3xl shadow-2xl p-6">
                  {/* Main Display Screen */}
                  <div className="w-full h-48 bg-gradient-to-br from-blue-600 via-purple-600 to-purple-800 rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-700/20"></div>
                    {/* Perfume Bottle in Display */}
                    <div className="relative z-10 w-16 h-24 bg-gradient-to-b from-purple-300 to-purple-500 rounded-lg shadow-lg border-2 border-white/30">
                      <div className="w-6 h-4 bg-gradient-to-b from-gray-300 to-gray-400 rounded-t-sm mx-auto"></div>
                    </div>
                  </div>

                  {/* Control Buttons */}
                  <div className="flex justify-between mb-4">
                    <div className="w-12 h-8 bg-gray-800 rounded-lg flex items-center justify-center">
                      <div className="w-6 h-6 border-2 border-white rounded-full"></div>
                    </div>
                    <div className="w-12 h-8 bg-gray-800 rounded-lg"></div>
                  </div>

                  {/* Dispensing Levers */}
                  <div className="flex justify-between space-x-2">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-8 h-16 bg-gray-800 rounded-full flex items-end justify-center pb-2">
                        <div className="w-4 h-4 bg-gray-600 rounded-full"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-60"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-200 rounded-full opacity-40"></div>
          </div>
        </div>
      </main>

      {/* Featured Products */}
      <section className="container mx-auto px-6 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Featured Fragrances
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover our most beloved scents, carefully crafted to capture the essence of elegance and sophistication.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="aspect-square bg-gradient-to-br from-rose-100 to-rose-200 rounded-2xl mb-6 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                <div className="w-16 h-20 bg-gradient-to-b from-rose-400 to-rose-600 rounded-lg shadow-lg"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Rose Elegance</h3>
              <p className="text-gray-600 mb-4">A delicate blend of Bulgarian rose and white musk</p>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900">$89</span>
                <Button size="sm" variant="outline">Add to Cart</Button>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="aspect-square bg-gradient-to-br from-amber-100 to-amber-200 rounded-2xl mb-6 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                <div className="w-16 h-20 bg-gradient-to-b from-amber-400 to-amber-600 rounded-lg shadow-lg"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Golden Sunset</h3>
              <p className="text-gray-600 mb-4">Warm notes of vanilla and sandalwood</p>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900">$95</span>
                <Button size="sm" variant="outline">Add to Cart</Button>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl mb-6 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                <div className="w-16 h-20 bg-gradient-to-b from-blue-400 to-blue-600 rounded-lg shadow-lg"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Ocean Breeze</h3>
              <p className="text-gray-600 mb-4">Fresh aquatic notes with hints of citrus</p>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900">$79</span>
                <Button size="sm" variant="outline">Add to Cart</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                <span className="text-xl font-bold">ScentTech</span>
              </div>
              <p className="text-gray-400">
                Revolutionizing fragrance experience with smart technology.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Shop</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">New Arrivals</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Best Sellers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Gift Sets</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Sale</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Shipping Info</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Returns</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Size Guide</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Instagram</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Facebook</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Newsletter</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 ScentTech. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
